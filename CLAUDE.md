# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `docker-compose down -v && docker-compose build --no-cahce && docker-compose up -d` - Start production server (***********:80)
- `npm run ldap` - Start LDAP authentication server (localhost:3002)

### Build & Deployment
- `npm run build` - Production build with optimizations
- `docker-compose down -v && docker-compose build --no-cahce && docker-compose up -d` - Start production server (***********:80)

### Code Quality
- `npm run lint` - Run ESLint code quality checks
- `npm run config:validate` - Validate environment configuration
- `npm run config:check` - Check for hardcoded configuration values

### Testing & Validation
- `npm run verify:intranet` - Verify intranet deployment readiness
- `npm run intranet:check` - Check external dependencies for intranet deployment

## Architecture Overview

### Frontend Architecture
- **React 19.1.0** with modern functional components and hooks
- **Vite 6.3.5** for fast development and optimized builds
- **Tailwind CSS 3.4.17** for responsive, utility-first styling
- **Component-based architecture** with clear separation of concerns

### Key Components Structure
- **HeroSection.jsx** - Main landing page with AI branding
- **APIKeySection.jsx** - API key management with caching and performance optimizations
- **DocumentationSection.jsx** - Lazy-loaded documentation with search functionality
- **LoginModal.jsx** - LDAP authentication interface
- **PixelLoader.jsx** - Custom pixel animation loader

### Services Layer
- **authManager.js** - LDAP authentication and session management
- **apiKeyService.js** - API key operations with intelligent caching
- **metricsService.js** - Platform metrics with fallback to mock data
- **ldapService.js** - LDAP connection and user validation
- **documentService.js** - Document management and search

### Authentication System
- **LDAP Integration** - Enterprise authentication with multiple environment support
- **JWT Tokens** - Secure session management
- **Role-based Access** - Different user permissions and access levels
- **Multi-environment Support** - Dev (240.10) and Prod (242.2) LDAP configurations

### API Integration
- **LiteLLM API** - AI model access and management
- **OpenAI SDK** - Direct AI model integration
- **Anthropic SDK** - Claude AI integration
- **Metrics API** - Real-time platform statistics

## Development Patterns

### Component Development
- Use React functional components with hooks
- Implement React.memo for performance optimization
- Use lazy loading for large components (React.lazy + Suspense)
- Follow the existing component structure in src/components/

### State Management
- Local state with useState/useEffect for component-specific data
- Context API for global state (auth, user info)
- Custom hooks for reusable logic
- Caching strategies for API responses

### API Integration
- Use axios for HTTP requests
- Implement error boundaries for graceful error handling
- Use caching for expensive operations (user info, metrics)
- Implement fallback mechanisms for API failures

### Styling Approach
- Tailwind CSS utility classes for responsive design
- Custom CSS for complex animations and effects
- Consistent color scheme: blue/cyan theme with dark mode support
- Mobile-first responsive design patterns

## Configuration Management

### Environment Variables
- All configuration through .env files (copy from env.example)
- Multiple LDAP environment support
- API endpoints configurable via environment variables
- Internal/external deployment modes

### Key Configuration Files
- **vite.config.js** - Build configuration with legacy browser support
- **tailwind.config.js** - Tailwind CSS customization
- **eslint.config.js** - Code quality rules
- **ecosystem.config.js** - PM2 deployment configuration

## Performance Optimizations

### Build Optimizations
- Code splitting with manual chunks for vendor libraries
- Terser minification with aggressive settings
- Tree shaking for unused code elimination
- Legacy browser support with polyfills

### Runtime Optimizations
- Intelligent caching for API responses
- Lazy loading for components and resources
- Debounced search and user input
- Optimized image loading strategies

### Intranet Deployment
- All external dependencies localized
- Font Awesome fonts and CSS stored locally
- No external CDN dependencies
- Offline-capable builds

## Common Issues & Solutions

### LDAP Authentication
- Check LDAP server connectivity and credentials
- Verify environment-specific LDAP configurations
- Test with predefined users (zhangsan/zhangsan123, etc.)

### API Integration
- LiteLLM API may not be available - service gracefully falls back to mock data
- API key validation requires proper MASTER_KEY configuration
- Check CORS settings for cross-origin requests

### Performance Issues
- DocumentationSection.jsx has been heavily optimized - avoid further modifications
- APIKeySection.jsx uses aggressive caching - clear cache when needed
- Service Worker caches GET requests only, not POST requests

### Build Issues
- Use Node.js 18+ for compatibility
- External dependencies are excluded from browser builds
- Check for hardcoded external URLs before intranet deployment

## Testing

### Test Users (LDAP)
- zhangsan/zhangsan123 (高级工程师)
- lisi/lisi456 (软件工程师)
- admin/admin123 (系统管理员)
- developer/dev123456 (开发工程师)

### Validation Steps
1. Start services: `npm run dev:full`
2. Test LDAP login with test users
3. Verify API key management functionality
4. Check platform metrics display
5. Test documentation search functionality

## Security Considerations

- JWT tokens for session management
- No hardcoded secrets in source code
- Environment-specific LDAP configurations
- API key encryption and secure storage
- CORS properly configured for service communication

## Recent Optimizations

- Service Worker POST request caching issue fixed
- DocumentationSection.jsx performance optimization (102KB → 15KB)
- APIKeySection.jsx caching and parallel loading
- Build process optimization with chunking strategies
- Intranet deployment readiness with all external dependencies localized