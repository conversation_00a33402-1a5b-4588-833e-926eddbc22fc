<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YNNX AI Platform API参考</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            max-width: 1000px; 
            margin: 0 auto; 
            padding: 40px 20px; 
            line-height: 1.6;
            color: #333;
        }
        .api-card { 
            background: white; 
            border-radius: 10px; 
            padding: 30px; 
            margin: 20px 0; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .endpoint {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .method.post { background: #007bff; }
        .method.get { background: #28a745; }
        .code {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .response {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        h2 { color: #1a73e8; border-bottom: 2px solid #1a73e8; padding-bottom: 10px; }
        h3 { color: #34a853; }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .param-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .required { color: #dc3545; font-weight: bold; }
        .optional { color: #6c757d; }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1557b0; }
    </style>
</head>
<body>
    <div class="api-card">
        <h1>🔌 YNNX AI Platform API参考</h1>
        <p><strong>基于现代化架构的平台API接口文档</strong></p>
        
        <h2>📋 API概述</h2>
        <p>YNNX AI Platform提供基于现代化架构的RESTful API，支持认证管理和平台功能。</p>
        
        <div class="endpoint">
            <h3>基础信息</h3>
            <ul>
                <li><strong>Base URL:</strong> <code>http://localhost:5173</code></li>
                <li><strong>协议支持:</strong> RESTful API + Schema.org</li>
                <li><strong>数据格式:</strong> JSON</li>
                <li><strong>编码:</strong> UTF-8</li>
            </ul>
        </div>

        <h2>🚀 核心API端点</h2>
        
        <div class="endpoint">
            <h3><span class="method post">POST</span>/api/auth/login</h3>
            <p><strong>智能问答API</strong> - 主要的自然语言查询接口</p>
            
            <h4>请求参数</h4>
            <table class="param-table">
                <tr>
                    <th>参数名</th>
                    <th>类型</th>
                    <th>必需</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>query</td>
                    <td>string</td>
                    <td><span class="required">必需</span></td>
                    <td>用户的自然语言查询内容</td>
                </tr>
                <tr>
                    <td>context</td>
                    <td>object</td>
                    <td><span class="optional">可选</span></td>
                    <td>查询上下文信息</td>
                </tr>
                <tr>
                    <td>context.source</td>
                    <td>string</td>
                    <td><span class="optional">可选</span></td>
                    <td>查询来源（如：chat_interface）</td>
                </tr>
            </table>
            
            <h4>请求示例</h4>
            <div class="code">
curl -X POST http://localhost:3002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "query": "平台有哪些页面功能？",
    "context": {
      "source": "chat_interface",
      "timestamp": "2025-01-27T10:00:00.000Z"
    }
  }'
            </div>
            
            <h4>响应格式 (Schema.org标准)</h4>
            <div class="response">
                <div class="code">
{
  "@context": "http://localhost:5173/local-resources/schema.org",
  "@type": "SearchResultsPage",
  "url": "http://localhost:3002/api/auth/login",
  "dateCreated": "2025-01-27T10:00:00.000Z",
  "provider": {
    "@type": "Organization", 
    "name": "YNNX AI Platform",
    "url": "http://localhost:5173"
  },
  "mainEntity": {
    "@type": "Question",
    "text": "平台有哪些页面功能？",
    "acceptedAnswer": {
      "@type": "Answer",
      "text": "🏠 **YNNX AI平台包含7个主要页面**...",
      "dateCreated": "2025-01-27T10:00:00.000Z",
      "author": {
        "@type": "Organization",
        "name": "YNNX AI Assistant"
      },
      "additionalProperty": [
        {
          "@type": "PropertyValue",
          "name": "responseType", 
          "value": "ai_generated"
        },
        {
          "@type": "PropertyValue",
          "name": "model",
          "value": "gpt-4"
        }
      ]
    }
  }
}
                </div>
            </div>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span>/api/auth/status</h3>
            <p><strong>服务信息API</strong> - 获取API服务状态和信息</p>
            
            <h4>响应示例</h4>
            <div class="response">
                <div class="code">
{
  "@context": "http://localhost:5173/local-resources/schema.org",
  "@type": "WebAPI",
  "name": "YNNX AI Platform Natural Language Web API", 
  "description": "基于现代化架构的认证和管理服务",
  "version": "1.0.0",
  "provider": {
    "@type": "Organization",
    "name": "YNNX AI Platform",
    "url": "http://localhost:5173"
  },
  "serviceStatus": {
    "initialized": true,
    "models": ["openai", "anthropic", "azure"]
  }
}
                </div>
            </div>
        </div>

        <h2>🔐 认证方式</h2>
        <div class="endpoint">
            <h3>API密钥认证</h3>
            <p>使用平台生成的API密钥进行认证：</p>
            <ol>
                <li>登录YNNX AI Platform</li>
                <li>进入"API密钥"页面</li>
                <li>点击"生成新密钥"</li>
                <li>在请求头中添加认证信息</li>
            </ol>
            
            <div class="code">
Authorization: Bearer sk-xxxxxxxxxxxxxxxxxxxxxxxx
            </div>
        </div>

        <h2>📊 支持的查询类型</h2>
        <div class="endpoint">
            <h3>平台功能查询</h3>
            <ul>
                <li>"平台有哪些页面？"</li>
                <li>"AI助手有什么功能？"</li>
                <li>"如何管理API密钥？"</li>
            </ul>
            
            <h3>工具下载查询</h3>
            <ul>
                <li>"如何下载Cline插件？"</li>
                <li>"JetBrains插件怎么安装？"</li>
                <li>"VS Code完整版在哪下载？"</li>
            </ul>
            
            <h3>技术支持查询</h3>
            <ul>
                <li>"支持哪些AI模型？"</li>
                <li>"技术架构是什么？"</li>
                <li>"有哪些文档资料？"</li>
            </ul>
        </div>

        <h2>❌ 错误处理</h2>
        <div class="endpoint">
            <h3>常见错误码</h3>
            <table class="param-table">
                <tr>
                    <th>状态码</th>
                    <th>错误类型</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>400</td>
                    <td>Invalid Query</td>
                    <td>查询参数为空或格式错误</td>
                </tr>
                <tr>
                    <td>401</td>
                    <td>Unauthorized</td>
                    <td>API密钥无效或缺失</td>
                </tr>
                <tr>
                    <td>500</td>
                    <td>Internal Error</td>
                    <td>服务器内部错误</td>
                </tr>
                <tr>
                    <td>503</td>
                    <td>Service Unavailable</td>
                    <td>AI服务暂时不可用</td>
                </tr>
            </table>
        </div>

        <h2>🔗 相关资源</h2>
        <div class="endpoint">
            <ul>
                <li><a href="/local-resources/search-help.html" target="_blank">📖 搜索帮助指南</a></li>
                <li><a href="/local-resources/search-help.html" target="_blank">🔍 搜索帮助</a></li>
                <li><a href="/local-resources/schema-org-reference.html" target="_blank">📋 Schema.org标准</a></li>
                <li><a href="#" target="_blank">🌐 技术文档</a></li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="window.close()">关闭文档</button>
            <button onclick="window.open('/local-resources/search-help.html', '_blank')">查看使用指南</button>
        </div>
    </div>
</body>
</html>