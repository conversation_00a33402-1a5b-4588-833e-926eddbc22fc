#!/bin/bash

# YNNX AI Platform LDAP认证服务器启动脚本

echo "🚀 启动YNNX LDAP认证服务器..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "ldapAuthServer.js" ]; then
    echo "❌ 错误: 请在server目录下运行此脚本"
    exit 1
fi

# 安装依赖包
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 检查环境变量文件
if [ ! -f "../../.env" ]; then
    echo "⚠️  警告: 未找到.env文件，将使用默认配置"
    echo "📄 请根据env.example创建.env文件并配置您的LDAP服务器信息"
fi

# 启动服务器
echo "🔗 启动LDAP认证服务器..."
echo "📍 服务地址: http://localhost:3002"
echo "🛑 按Ctrl+C停止服务"
echo ""

# 加载环境变量并启动
if [ -f "../../.env" ]; then
    export $(cat ../../.env | grep -v '^#' | xargs)
fi

node ldapAuthServer.js 