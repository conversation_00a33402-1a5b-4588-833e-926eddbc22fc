<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM修复测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8fafc;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h1>🔧 DOM操作修复测试</h1>
        <p>此页面用于测试React DOM操作错误的修复情况</p>
        
        <div class="status info">
            <strong>测试目标:</strong> 验证智能问题路由功能不再产生DOM操作错误
        </div>
    </div>

    <div class="test-section">
        <h2>📋 修复内容总结</h2>
        <ul>
            <li>✅ 修复了所有map函数中缺少稳定key的问题</li>
            <li>✅ 添加了防御性编程，避免null/undefined访问</li>
            <li>✅ 移除了可能导致递归渲染的代码</li>
            <li>✅ 优化了条件渲染逻辑</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 测试操作</h2>
        <p>请在AI助手中尝试以下操作来验证修复效果：</p>
        
        <div class="test-button-group">
            <button class="test-button" onclick="copyToClipboard('工具下载有什么？')">
                测试页面内容问题
            </button>
            <button class="test-button" onclick="copyToClipboard('如何安装开发工具？')">
                测试文档搜索问题
            </button>
            <button class="test-button" onclick="copyToClipboard('详细介绍页面上的所有工具')">
                测试混合模式问题
            </button>
        </div>
        
        <div class="status info">
            <strong>使用方法:</strong> 点击按钮复制测试问题，然后在AI助手中粘贴并发送
        </div>
    </div>

    <div class="test-section">
        <h2>📊 错误监控</h2>
        <div id="errorStatus" class="status success">
            ✅ 当前无错误
        </div>
        
        <h3>错误日志:</h3>
        <div id="errorLog" class="log">暂无错误记录</div>
        
        <button class="test-button" onclick="clearErrorLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h2>🔍 页面内容检测</h2>
        <p>此页面包含以下测试内容，用于验证智能路由功能：</p>
        
        <div class="download-tools">
            <h3>开发工具列表</h3>
            <ul>
                <li data-tool="cline">Cline VS Code插件 - AI编程助手</li>
                <li data-tool="jetbrains">JetBrains AI Assistant - IDE插件</li>
                <li data-tool="docker">Docker Desktop - 容器化平台</li>
                <li data-tool="git">Git for Windows - 版本控制</li>
            </ul>
        </div>
        
        <div class="download-buttons">
            <button class="test-button" data-download="cline">下载 Cline</button>
            <button class="test-button" data-download="jetbrains">下载 JetBrains AI</button>
            <button class="test-button disabled" disabled data-download="docker">Docker (暂不可用)</button>
        </div>
        
        <div class="download-links">
            <a href="/downloads/cline.vsix" download>Cline插件下载</a> |
            <a href="/downloads/jetbrains-ai.zip" download>JetBrains AI下载</a> |
            <a href="/downloads/guide.pdf" download>使用指南PDF</a>
        </div>
    </div>

    <script>
        // 错误监控
        let errorCount = 0;
        const errorLog = document.getElementById('errorLog');
        const errorStatus = document.getElementById('errorStatus');

        // 监听全局错误
        window.addEventListener('error', function(event) {
            logError('JavaScript Error', event.error?.toString() || event.message);
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            logError('Unhandled Promise Rejection', event.reason?.toString() || 'Unknown error');
        });

        // 监听React错误（如果有的话）
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('removeChild') || message.includes('React') || message.includes('DOM')) {
                logError('React/DOM Error', message);
            }
            originalConsoleError.apply(console, args);
        };

        function logError(type, message) {
            errorCount++;
            const timestamp = new Date().toLocaleTimeString();
            const errorEntry = `[${timestamp}] ${type}: ${message}\n`;
            
            errorLog.textContent += errorEntry;
            errorLog.scrollTop = errorLog.scrollHeight;
            
            // 更新状态
            errorStatus.className = 'status error';
            errorStatus.textContent = `❌ 检测到 ${errorCount} 个错误`;
        }

        function clearErrorLog() {
            errorCount = 0;
            errorLog.textContent = '暂无错误记录';
            errorStatus.className = 'status success';
            errorStatus.textContent = '✅ 当前无错误';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 临时显示复制成功提示
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#007bff';
                }, 1500);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制: ' + text);
            });
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('DOM修复测试页面加载完成');
            console.log('开始监控React DOM操作错误...');
            
            // 模拟一些可能触发错误的操作
            setTimeout(() => {
                console.log('测试页面运行正常，未检测到DOM操作错误');
            }, 2000);
        });
    </script>
</body>
</html>
