{"timestamp": "2025-06-19T15:40:20.440Z", "metrics": {"buildSize": {"total": 458715.79, "js": 1398.74, "css": 129.71, "fonts": 971.7, "images": 4.08, "other": 456211.56}, "dependencies": {"production": 15, "development": 14, "total": 29, "list": {"production": ["@anthropic-ai/sdk", "axios", "concurrently", "cors", "dotenv", "express", "ldapjs", "lucide-react", "node-fetch", "openai", "react", "react-dom", "react-icons", "react-router-dom", "uuid"], "development": ["@eslint/js", "@types/react", "@types/react-dom", "@vitejs/plugin-legacy", "@vitejs/plugin-react", "autoprefixer", "core-js", "eslint", "eslint-plugin-react-hooks", "eslint-plugin-react-refresh", "globals", "postcss", "tailwindcss", "vite"]}}, "caching": {"serviceWorker": true, "viteCache": true, "features": ["Stale While Revalidate", "Image Optimization", "Network First"]}, "codeSplitting": {"total": 32, "mainChunk": "index-CxTVoU01.js", "vendorChunks": ["vendor-small-7Y5hK7EX.js", "vendor-small-legacy-CBltNgRP.js"], "lazyChunks": ["APIKeySection-CveBZnjb.js", "APIKeySection-legacy-D2KcwUJn.js", "LoginModal-CLqUA0lq.js", "LoginModal-legacy-BsW0Sa_u.js", "NewsSection-CadXj4lf.js", "NewsSection-legacy-CRa6aEah.js", "comp-ai-Cs2QdQjF.js", "comp-ai-legacy-mLc22JWe.js", "comp-chat-legacy-JyglpMcc.js", "comp-chat-tTIe6JOA.js", "comp-docs-BICwdXsb.js", "comp-docs-legacy-B32F1sme.js", "comp-downloads-Bt9wv95v.js", "comp-downloads-legacy-CGgHg7R6.js", "icons-fa-DGD0NjPb.js", "icons-fa-legacy-BFmzB0Zb.js", "icons-hi-D-eBkl-F.js", "icons-hi-legacy-BbM2qtaf.js", "icons-other-legacy-BoX7fwdW.js", "icons-other-usl3bEPC.js", "polyfills-BkMdUuW0.js", "polyfills-CatGmP1P.js", "polyfills-legacy-C_H8aM2s.js", "polyfills-legacy-Dq68ioDu.js", "react-CknWPsgO.js", "react-dom-B53mNZTn.js", "react-dom-legacy-Dnb_9uDt.js", "react-legacy-D32Iab86.js"]}}, "optimizations": {}, "recommendations": [{"type": "warning", "category": "JavaScript", "message": "JavaScript 包体积过大 (1398.74 KB)，建议进一步代码分割"}, {"type": "warning", "category": "CSS", "message": "CSS 体积较大 (129.71 KB)，检查是否有未使用的样式"}]}