#!/bin/bash

echo "🚀 启动LDAP认证服务器..."

# 检查是否在项目根目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查node_modules是否存在
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 进入server目录
cd src/server

# 检查LDAP认证服务器文件是否存在
if [ ! -f "ldapAuthServer.js" ]; then
    echo "❌ 找不到LDAP认证服务器文件"
    exit 1
fi

# 启动服务器
echo "🔗 启动LDAP认证服务器 (端口 3002)..."
node ldapAuthServer.js 