# Cline 插件 - 最新版本

本目录包含 Cline AI 编程助手插件的最新版本文件。

## 关于 Cline
Cline 是一个强大的 AI 编程助手，支持：
- 智能代码生成和补全
- 自然语言编程
- 代码重构和优化
- 错误检测和修复

## 文件说明
- `.vsix` 文件 - VS Code 插件安装包
- 文件名格式：`cline-[版本号].vsix`

## 安装方法
1. 下载 `.vsix` 文件
2. 打开 VS Code
3. 按 `Ctrl+Shift+P` (Mac: `Cmd+Shift+P`)
4. 输入：`Extensions: Install from VSIX`
5. 选择下载的文件
6. 重启 VS Code

## 配置要求
- VS Code 1.74.0+
- 需要配置 API 密钥
- 稳定的网络连接

## 更新频率
本目录中的文件会定期更新，建议：
- 定期检查新版本
- 更新前备份重要设置
- 查看更新日志了解新功能

---
*最新更新时间会在文件名中体现，请选择最新的文件* 