version: '3.8'

services:
  # 应用服务
  app:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        VITE_LDAP_API_URL: /api/ldap
        VITE_LITELLM_API_BASE: /api/litellm
    container_name: ynnx-ai-platform
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - TZ=Asia/Shanghai
      - LDAP_AUTH_PORT=3002
      - LDAP_DEFAULT_HOST=0.0.0.0
      - LDAP_PRIMARY_HOST=0.0.0.0
      - CORS_ORIGIN=https://***********,http://***********,https://localhost,http://localhost
      # LDAP 基础配置
      - LDAP_DEFAULT_ENVIRONMENT=DEVVDI_ENV
      - LDAP_CONNECTION_TIMEOUT=20
      - LDAP_SEARCH_TIMEOUT=10
      - LDAP_MAX_RETRIES=2
      # DEVVDI 环境完整配置
      - LDAP_DEVVDI_ENV_URL=ldap://***********:11389
      - LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_BIND_DN=
      - LDAP_DEVVDI_ENV_BIND_PASSWORD=
      - LDAP_DEVVDI_ENV_USER_SEARCH_BASE=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
      - LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USER_DOMAIN=@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USE_DIRECT_BIND=true
      - LDAP_DEVVDI_ENV_NAME=240.10云桌面环境
      - LDAP_DEVVDI_ENV_DESCRIPTION=DEVVDI Active Directory环境 - 端口389
      # VDI 环境完整配置
      - LDAP_VDI_ENV_URL=ldap://***********:12389
      - LDAP_VDI_ENV_BASE_DN=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_BIND_DN=
      - LDAP_VDI_ENV_BIND_PASSWORD=
      - LDAP_VDI_ENV_USER_SEARCH_BASE=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_USER_FILTER=(userPrincipalName={{username}}@VDI.YNNX.COM)
      - LDAP_VDI_ENV_USER_DN_PATTERN={{username}}@VDI.YNNX.COM
      - LDAP_VDI_ENV_USER_DOMAIN=@VDI.YNNX.COM
      - LDAP_VDI_ENV_USE_DIRECT_BIND=true
      - LDAP_VDI_ENV_NAME=242.2云桌面环境
      - LDAP_VDI_ENV_DESCRIPTION=VDI Active Directory环境 - 端口389
      # LLM 配置
      - OPENAI_BASE_URL=https://***********:4000/v1
      - OPENAI_MODEL=qwen3-235b-a22b
      - OPENAI_API_KEY=sk-ynnx-llm-20250530
      - ENABLE_OPENAI=true
      - LITELLM_MASTER_KEY=sk-ynnx-llm-20250530
    volumes:
      - ./logs:/var/log/ynnx-ai
      - ./dist:/app/dist
    ports:
      - "3002:3002"
    networks:
      - ynnx-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ynnx-nginx
    restart: unless-stopped
    ports:
      - "80:80"        # HTTP (重定向到HTTPS)
      - "443:443"      # HTTPS
      - "8443:8443"    # WebSocket专用端口 (HTTP/1.1)
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./dist:/usr/share/nginx/html:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ynnx-network
    depends_on:
      - app



networks:
  ynnx-network:
    driver: bridge

# ===================================================================
# 外部服务说明
# ===================================================================
#
# LobeChat 智能对话服务 (外部服务)
# - 服务地址: http://***********:3210
# - 代理路径: /chat/
# - 认证方式: 通过 nginx 代理传递平台认证信息
# - 文档: docs/lobechat-integration.md
#
# 注意: LobeChat 作为独立服务运行，不包含在此 docker-compose 中
# 需要单独部署和配置，详见集成文档
# ===================================================================