#!/usr/bin/env node

/**
 * YNNX AI Platform 内网部署构建脚本
 * 确保构建产物完全适用于内网环境，无外部依赖
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始内网部署构建...\n');

// 1. 检查内网配置文件
console.log('📋 1. 检查内网配置...');
if (!fs.existsSync('.env.intranet')) {
  console.error('❌ 未找到 .env.intranet 配置文件');
  process.exit(1);
}

// 备份当前 .env 文件
if (fs.existsSync('.env')) {
  fs.copyFileSync('.env', '.env.backup');
  console.log('✅ 已备份当前 .env 文件');
}

// 使用内网配置
fs.copyFileSync('.env.intranet', '.env');
console.log('✅ 已切换到内网配置\n');

try {
  // 2. 清理构建目录
  console.log('🧹 2. 清理构建目录...');
  if (fs.existsSync('dist')) {
    execSync('rm -rf dist', { stdio: 'inherit' });
  }
  console.log('✅ 构建目录已清理\n');

  // 3. 安装依赖（确保离线可用）
  console.log('📦 3. 检查依赖...');
  try {
    execSync('npm ci --offline', { stdio: 'inherit' });
    console.log('✅ 离线依赖安装成功');
  } catch (error) {
    console.log('⚠️ 离线安装失败，尝试在线安装...');
    execSync('npm ci', { stdio: 'inherit' });
    console.log('✅ 在线依赖安装成功');
  }
  console.log('');

  // 4. 运行内网优化脚本
  console.log('🔧 4. 运行内网优化...');
  if (fs.existsSync('scripts/prepare-intranet-deployment.cjs')) {
    execSync('node scripts/prepare-intranet-deployment.cjs', { stdio: 'inherit' });
    console.log('✅ 内网优化完成');
  } else {
    console.log('⚠️ 内网优化脚本不存在，跳过');
  }
  console.log('');

  // 5. 构建项目
  console.log('🏗️ 5. 构建项目...');
  execSync('NODE_ENV=production VITE_INTRANET_MODE=true npm run build', { stdio: 'inherit' });
  console.log('✅ 项目构建完成\n');

  // 6. 验证构建产物
  console.log('🔍 6. 验证构建产物...');
  
  // 检查是否存在外部链接
  const checkExternalLinks = () => {
    try {
      const result = execSync('find dist -type f \\( -name "*.js" -o -name "*.css" -o -name "*.html" \\) -exec grep -l "https://" {} \\;', 
        { encoding: 'utf-8' });
      
      if (result.trim()) {
        console.log('⚠️ 发现包含外部链接的文件:');
        console.log(result);
        
        // 详细检查每个文件
        const files = result.trim().split('\n');
        files.forEach(file => {
          try {
            const matches = execSync(`grep -n "https://" "${file}"`, { encoding: 'utf-8' });
            console.log(`\n📄 ${file}:`);
            console.log(matches);
          } catch (e) {
            // 文件可能被删除或无权限
          }
        });
        
        return false;
      } else {
        console.log('✅ 未发现外部链接');
        return true;
      }
    } catch (error) {
      console.log('✅ 未发现外部链接');
      return true;
    }
  };

  const isClean = checkExternalLinks();
  
  // 检查关键文件
  const criticalFiles = [
    'dist/index.html',
    'dist/assets',
    'dist/favicon.svg'
  ];
  
  let allFilesExist = true;
  criticalFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
      allFilesExist = false;
    }
  });

  // 7. 生成部署报告
  console.log('\n📊 7. 生成部署报告...');
  
  const deploymentReport = {
    timestamp: new Date().toISOString(),
    buildMode: 'intranet',
    externalLinksClean: isClean,
    criticalFilesPresent: allFilesExist,
    buildSize: getBuildSize(),
    recommendations: generateRecommendations(isClean, allFilesExist)
  };

  fs.writeFileSync('intranet-build-report.json', JSON.stringify(deploymentReport, null, 2));
  console.log('✅ 部署报告已生成: intranet-build-report.json\n');

  // 8. 输出结果
  if (isClean && allFilesExist) {
    console.log('🎉 内网构建成功！');
    console.log('✅ 构建产物已准备就绪，可用于内网部署');
    console.log('\n📋 下一步操作:');
    console.log('1. 将 dist 目录部署到内网服务器');
    console.log('2. 配置内网域名和反向代理');
    console.log('3. 运行健康检查验证部署');
  } else {
    console.log('⚠️ 内网构建完成，但存在问题');
    console.log('请检查上述警告并手动修复');
  }

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
} finally {
  // 恢复原始 .env 文件
  if (fs.existsSync('.env.backup')) {
    fs.copyFileSync('.env.backup', '.env');
    fs.unlinkSync('.env.backup');
    console.log('\n🔄 已恢复原始 .env 配置');
  }
}

// 辅助函数
function getBuildSize() {
  try {
    const result = execSync('du -sh dist', { encoding: 'utf-8' });
    return result.trim().split('\t')[0];
  } catch (error) {
    return 'unknown';
  }
}

function generateRecommendations(isClean, allFilesExist) {
  const recommendations = [];
  
  if (!isClean) {
    recommendations.push('修复构建产物中的外部链接引用');
  }
  
  if (!allFilesExist) {
    recommendations.push('确保所有关键文件都已正确生成');
  }
  
  recommendations.push('在内网环境中测试应用功能');
  recommendations.push('配置内网DNS解析');
  recommendations.push('设置适当的缓存策略');
  
  return recommendations;
}
