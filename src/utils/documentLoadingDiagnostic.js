/**
 * 文档加载诊断工具
 * 用于诊断和修复文档库加载问题
 */

import enhancedDocumentService from '../services/documentService';

class DocumentLoadingDiagnostic {
  constructor() {
    this.diagnosticResults = [];
    this.fixAttempts = [];
  }

  /**
   * 运行完整的诊断流程
   */
  async runFullDiagnostic() {
    console.log('[文档诊断] 开始运行完整诊断...');
    
    const results = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      },
      recommendations: []
    };

    // 测试1: 检查配置文件可访问性
    await this.testConfigFileAccess(results);
    
    // 测试2: 检查文档文件可访问性
    await this.testDocumentFilesAccess(results);
    
    // 测试3: 检查文档服务状态
    await this.testDocumentServiceStatus(results);
    
    // 测试4: 检查缓存状态
    await this.testCacheStatus(results);
    
    // 测试5: 测试搜索功能
    await this.testSearchFunctionality(results);

    // 生成建议
    this.generateRecommendations(results);
    
    console.log('[文档诊断] 诊断完成:', results);
    return results;
  }

  /**
   * 测试配置文件可访问性
   */
  async testConfigFileAccess(results) {
    const test = {
      name: '配置文件可访问性',
      status: 'running',
      details: []
    };

    try {
      const response = await fetch('/docs/config.json');
      if (response.ok) {
        const config = await response.json();
        test.status = 'passed';
        test.details.push(`配置文件加载成功，包含 ${Object.keys(config.documents).length} 个分类`);
        results.summary.passed++;
      } else {
        test.status = 'failed';
        test.details.push(`HTTP ${response.status}: ${response.statusText}`);
        results.summary.failed++;
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`配置文件加载失败: ${error.message}`);
      results.summary.failed++;
    }

    results.tests.push(test);
  }

  /**
   * 测试文档文件可访问性
   */
  async testDocumentFilesAccess(results) {
    const test = {
      name: '文档文件可访问性',
      status: 'running',
      details: []
    };

    try {
      // 获取配置
      const configResponse = await fetch('/docs/config.json');
      if (!configResponse.ok) {
        test.status = 'failed';
        test.details.push('无法获取配置文件');
        results.summary.failed++;
        results.tests.push(test);
        return;
      }

      const config = await configResponse.json();
      let successCount = 0;
      let totalCount = 0;

      // 测试每个文档文件
      for (const [category, docs] of Object.entries(config.documents)) {
        for (const doc of docs) {
          totalCount++;
          try {
            const docResponse = await fetch(`/docs/${doc.file}`);
            if (docResponse.ok) {
              successCount++;
            } else {
              test.details.push(`${doc.file}: HTTP ${docResponse.status}`);
            }
          } catch (error) {
            test.details.push(`${doc.file}: ${error.message}`);
          }
        }
      }

      if (successCount === totalCount) {
        test.status = 'passed';
        test.details.unshift(`所有 ${totalCount} 个文档文件都可以正常访问`);
        results.summary.passed++;
      } else if (successCount > 0) {
        test.status = 'warning';
        test.details.unshift(`${successCount}/${totalCount} 个文档文件可以访问`);
        results.summary.warnings++;
      } else {
        test.status = 'failed';
        test.details.unshift('没有文档文件可以访问');
        results.summary.failed++;
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`测试失败: ${error.message}`);
      results.summary.failed++;
    }

    results.tests.push(test);
  }

  /**
   * 测试文档服务状态
   */
  async testDocumentServiceStatus(results) {
    const test = {
      name: '文档服务状态',
      status: 'running',
      details: []
    };

    try {
      const stats = await enhancedDocumentService.getDocumentStats();
      
      if (stats.canSearch) {
        test.status = 'passed';
        test.details.push(`文档服务正常，已加载 ${stats.totalDocuments} 个文档`);
        test.details.push(`总计 ${stats.totalWords} 个词，${stats.totalSections} 个章节`);
        results.summary.passed++;
      } else {
        test.status = 'warning';
        test.details.push(`文档服务状态: ${stats.loadingStatus.message}`);
        test.details.push(`当前已缓存 ${stats.totalDocuments} 个文档`);
        results.summary.warnings++;
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`文档服务检查失败: ${error.message}`);
      results.summary.failed++;
    }

    results.tests.push(test);
  }

  /**
   * 测试缓存状态
   */
  async testCacheStatus(results) {
    const test = {
      name: '缓存状态',
      status: 'running',
      details: []
    };

    try {
      // 检查文档缓存
      const cacheSize = enhancedDocumentService.documentCache.size;
      const isLoaded = enhancedDocumentService.allDocumentsLoaded;
      const isLoading = enhancedDocumentService.loading;

      test.details.push(`缓存大小: ${cacheSize} 个文档`);
      test.details.push(`加载状态: ${isLoaded ? '已完成' : '未完成'}`);
      test.details.push(`正在加载: ${isLoading ? '是' : '否'}`);

      if (isLoaded && cacheSize > 0) {
        test.status = 'passed';
        results.summary.passed++;
      } else if (isLoading) {
        test.status = 'warning';
        test.details.push('文档正在加载中...');
        results.summary.warnings++;
      } else {
        test.status = 'failed';
        test.details.push('缓存为空或加载失败');
        results.summary.failed++;
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`缓存检查失败: ${error.message}`);
      results.summary.failed++;
    }

    results.tests.push(test);
  }

  /**
   * 测试搜索功能
   */
  async testSearchFunctionality(results) {
    const test = {
      name: '搜索功能',
      status: 'running',
      details: []
    };

    try {
      // 测试简单搜索
      const searchResults = await enhancedDocumentService.searchDocuments('安装', { maxResults: 3 });
      
      if (searchResults.length > 0) {
        test.status = 'passed';
        test.details.push(`搜索功能正常，找到 ${searchResults.length} 个结果`);
        test.details.push(`测试查询: "安装"`);
        results.summary.passed++;
      } else {
        test.status = 'warning';
        test.details.push('搜索功能可用但未找到结果');
        test.details.push('可能是文档内容问题或搜索词不匹配');
        results.summary.warnings++;
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`搜索功能测试失败: ${error.message}`);
      results.summary.failed++;
    }

    results.tests.push(test);
  }

  /**
   * 生成修复建议
   */
  generateRecommendations(results) {
    const recommendations = [];

    // 基于测试结果生成建议
    results.tests.forEach(test => {
      if (test.status === 'failed') {
        switch (test.name) {
          case '配置文件可访问性':
            recommendations.push('检查 public/docs/config.json 文件是否存在且格式正确');
            break;
          case '文档文件可访问性':
            recommendations.push('检查 public/docs/ 目录下的文档文件是否存在');
            recommendations.push('确认文档文件路径与配置文件中的路径一致');
            break;
          case '文档服务状态':
            recommendations.push('重启文档服务或刷新页面');
            recommendations.push('检查网络连接');
            break;
          case '缓存状态':
            recommendations.push('清理文档缓存并重新加载');
            recommendations.push('检查内存使用情况');
            break;
          case '搜索功能':
            recommendations.push('检查文档内容是否包含可搜索的文本');
            recommendations.push('验证搜索索引是否正确构建');
            break;
        }
      }
    });

    // 通用建议
    if (results.summary.failed > 0) {
      recommendations.push('刷新页面重试');
      recommendations.push('检查浏览器控制台是否有错误信息');
      recommendations.push('确认服务器正常运行');
    }

    results.recommendations = recommendations;
  }

  /**
   * 尝试自动修复问题
   */
  async attemptAutoFix() {
    console.log('[文档诊断] 尝试自动修复...');
    
    const fixes = [];

    try {
      // 修复1: 清理并重新加载缓存
      enhancedDocumentService.clearCache();
      fixes.push('已清理文档缓存');

      // 修复2: 重新预加载文档
      await enhancedDocumentService.preloadAllDocuments();
      fixes.push('已重新预加载文档');

      console.log('[文档诊断] 自动修复完成:', fixes);
      return { success: true, fixes };
    } catch (error) {
      console.error('[文档诊断] 自动修复失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 创建全局实例
const documentLoadingDiagnostic = new DocumentLoadingDiagnostic();

export default documentLoadingDiagnostic;
