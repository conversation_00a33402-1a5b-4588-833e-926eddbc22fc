<!DOCTYPE html>
<html>
<head>
    <title>LobeChat 加载测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
        #log { background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>LobeChat 加载诊断</h1>
    
    <div id="status" class="status info">正在加载 LobeChat...</div>
    
    <div>
        <h3>控制台日志:</h3>
        <div id="log"></div>
    </div>
    
    <div>
        <h3>LobeChat iframe:</h3>
        <iframe id="lobechat" src="/chat/"></iframe>
    </div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        const iframe = document.getElementById('lobechat');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        // 监听所有错误
        window.addEventListener('error', function(e) {
            addLog(`JavaScript 错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });
        
        // 监听未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', function(e) {
            addLog(`未处理的 Promise 拒绝: ${e.reason}`, 'error');
        });
        
        // 监听 iframe 加载事件
        iframe.onload = function() {
            addLog('iframe 加载完成', 'success');
            status.textContent = 'LobeChat iframe 已加载';
            status.className = 'status success';
            
            // 尝试检查 iframe 内容
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (iframeDoc) {
                    const title = iframeDoc.title;
                    addLog(`iframe 页面标题: ${title}`, 'info');
                    
                    // 检查是否有错误信息
                    const errorElements = iframeDoc.querySelectorAll('.error, .next-error-h1, [class*="error"]');
                    if (errorElements.length > 0) {
                        addLog(`发现 ${errorElements.length} 个错误元素`, 'error');
                    }
                    
                    // 检查是否有加载指示器
                    const loadingElements = iframeDoc.querySelectorAll('[class*="loading"], [class*="spinner"]');
                    if (loadingElements.length > 0) {
                        addLog(`发现 ${loadingElements.length} 个加载指示器`, 'info');
                    }
                } else {
                    addLog('无法访问 iframe 内容（跨域限制）', 'info');
                }
            } catch (e) {
                addLog(`检查 iframe 内容时出错: ${e.message}`, 'error');
            }
        };
        
        iframe.onerror = function() {
            addLog('iframe 加载失败', 'error');
            status.textContent = 'LobeChat iframe 加载失败';
            status.className = 'status error';
        };
        
        // 定期检查 iframe 状态
        let checkCount = 0;
        const checkInterval = setInterval(function() {
            checkCount++;
            addLog(`状态检查 #${checkCount}`, 'info');
            
            if (checkCount >= 30) { // 30秒后停止检查
                clearInterval(checkInterval);
                addLog('状态检查已停止', 'info');
            }
        }, 1000);
        
        addLog('诊断脚本已启动', 'success');
    </script>
</body>
</html>
