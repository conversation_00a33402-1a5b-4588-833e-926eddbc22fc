import React, { useState } from 'react';
import { FaUserCircle, FaSignOutAlt } from 'react-icons/fa';
import { HiMenuAlt3, HiX } from 'react-icons/hi';
import UserStatusIndicator from './UserStatusIndicator';

const Navbar = ({ user, onLogin, onLogout }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { label: '首页', href: '#home' },
    { label: '平台数据', href: '#features' },
    { label: 'API密钥', href: '#api-key' },
    { label: 'Web IDE', href: '#webide' },
    { label: '智能对话', href: '#chat' },
    { label: '工具下载', href: '#downloads' },
    { label: '文档中心', href: '#docs' },
    { label: '最新动态', href: '#news' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-40 bg-black/80 backdrop-blur-xl border-b border-gray-800 animate-fade-in-up">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center space-x-4 hover:scale-105 transition-transform duration-200">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 to-purple-500 blur-lg opacity-60 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-cyan-400 via-blue-400 to-purple-500 text-black font-black text-2xl px-4 py-2 rounded-lg shadow-lg">
                YNNX
              </div>
            </div>
            <span className="text-white font-black text-2xl bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
              AI Platform
            </span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.label}
                href={item.href}
                className="text-gray-300 hover:text-cyan-400 hover:-translate-y-0.5 transition-all duration-200 font-semibold text-lg"
              >
                {item.label}
              </a>
            ))}
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-white text-lg font-bold">{user.name}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-cyan-400 text-sm font-medium">已登录</p>
                    <UserStatusIndicator user={user} />
                  </div>
                </div>
                <button
                  onClick={onLogout}
                  className="bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded-lg transition-all duration-200 hover:scale-105"
                >
                  <FaSignOutAlt />
                </button>
              </div>
            ) : (
              <button
                onClick={onLogin}
                className="bg-gradient-to-r from-cyan-400 to-blue-500 text-black px-6 py-3 rounded-lg font-bold text-lg flex items-center space-x-2 shadow-lg hover:scale-105 transition-transform duration-200"
              >
                <FaUserCircle />
                <span>登录</span>
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden text-white p-2"
          >
            {isMobileMenuOpen ? <HiX size={24} /> : <HiMenuAlt3 size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden overflow-hidden bg-gray-900/95 backdrop-blur-xl transition-all duration-300 ${
          isMobileMenuOpen ? 'max-h-96' : 'max-h-0'
        }`}
      >
        <div className="px-4 py-4 space-y-3">
          {navItems.map((item) => (
            <a
              key={item.label}
              href={item.href}
              className="block text-gray-300 hover:text-cyan-400 py-2 transition-colors duration-200"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {item.label}
            </a>
          ))}
          <div className="pt-4 border-t border-gray-800">
            {user ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FaUserCircle className="text-gray-400" size={32} />
                  <div>
                    <p className="text-white text-sm font-medium">{user.name}</p>
                    <p className="text-gray-400 text-xs">已登录</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    onLogout();
                    setIsMobileMenuOpen(false);
                  }}
                  className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-400 py-2 rounded-lg transition-colors duration-200"
                >
                  退出登录
                </button>
              </div>
            ) : (
              <button
                onClick={() => {
                  onLogin();
                  setIsMobileMenuOpen(false);
                }}
                className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 text-black py-3 rounded-lg font-bold text-lg shadow-lg"
              >
                登录
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar; 