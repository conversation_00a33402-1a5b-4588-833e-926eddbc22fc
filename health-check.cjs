#!/usr/bin/env node

const http = require('http');

const options = {
  hostname: '127.0.0.1',
  port: 3001,
  path: '/health',
  method: 'GET',
  timeout: 5000,
  family: 4  // 强制使用IPv4
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed');
    process.exit(0);
  } else {
    console.log(`Health check failed with status: ${res.statusCode}`);
    process.exit(1);
  }
});

req.on('error', (err) => {
  console.log(`Health check failed: ${err.message}`);
  process.exit(1);
});

req.on('timeout', () => {
  console.log('Health check timeout');
  req.destroy();
  process.exit(1);
});

req.end(); 