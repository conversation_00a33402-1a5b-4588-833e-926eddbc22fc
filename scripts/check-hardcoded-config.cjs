#!/usr/bin/env node

// 检查项目中硬编码配置的脚本
const fs = require('fs');
const path = require('path');

console.log('🔍 检查项目中的硬编码配置...\n');

// 要搜索的硬编码模式
const hardcodedPatterns = [
  {
    pattern: /192\.168\.\d+\.\d+/g,
    name: 'IP地址',
    severity: 'high'
  },
  {
    pattern: /:\d{4,5}(?![0-9])/g,
    name: '端口号',
    severity: 'medium'
  },
  {
    pattern: /ldap:\/\/[^\/\s]+/g,
    name: 'LDAP URL',
    severity: 'high'
  },
  {
    pattern: /http:\/\/[^\/\s]+:\d+/g,
    name: 'HTTP服务地址',
    severity: 'high'
  }
];

// 要排除的文件和目录
const excludePatterns = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '.env',
  '.env.example',
  'check-hardcoded-config.js',
  'check-hardcoded-config.cjs',
  '*.md',  // 文档文件可能包含示例
  '*.log',
  '.hardcode-whitelist.json', // 硬编码白名单文件
  'DEPLOYMENT.md' // 部署文档可能包含示例配置
];

// 要检查的文件扩展名
const includeExtensions = ['.js', '.jsx', '.ts', '.tsx', '.json', '.vue'];

// 加载白名单配置
function loadWhitelist() {
  const whitelistPath = path.join(__dirname, '..', '.hardcode-whitelist.json');
  try {
    if (fs.existsSync(whitelistPath)) {
      const whitelistContent = fs.readFileSync(whitelistPath, 'utf8');
      return JSON.parse(whitelistContent);
    }
  } catch (error) {
    console.warn('⚠️  无法加载白名单文件，将使用默认检查规则');
  }
  return null;
}

// 检查是否在白名单中
function isWhitelisted(issue, whitelist) {
  if (!whitelist || !whitelist.whitelist) return false;

  const { patterns, acceptable_defaults } = whitelist.whitelist;

  // 检查可接受的默认值
  if (acceptable_defaults && acceptable_defaults.includes(issue.match)) {
    // 进一步检查上下文是否包含环境变量
    if (issue.context.includes('process.env') || issue.context.includes('import.meta.env')) {
      return true;
    }
  }

  // 检查模式匹配
  if (patterns) {
    for (const patternConfig of patterns) {
      try {
        const regex = new RegExp(patternConfig.pattern);
        if (regex.test(issue.match) || regex.test(issue.context)) {
          // 如果指定了文件列表，检查文件是否匹配
          if (patternConfig.files) {
            const fileName = path.basename(issue.file);
            const fileMatches = patternConfig.files.some(f => 
              path.basename(f) === fileName || issue.file.includes(f)
            );
            if (fileMatches) return true;
          } else {
            return true;
          }
        }
      } catch (error) {
        console.warn(`⚠️  白名单模式无效: ${patternConfig.pattern}`);
      }
    }
  }

  return false;
}

function shouldExcludeFile(filePath) {
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(path.basename(filePath));
    }
    return filePath.includes(pattern);
  });
}

function checkFile(filePath, whitelist) {
  if (shouldExcludeFile(filePath)) {
    return [];
  }

  const ext = path.extname(filePath);
  if (!includeExtensions.includes(ext)) {
    return [];
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];

    hardcodedPatterns.forEach(({ pattern, name, severity }) => {
      const matches = content.match(pattern);
      if (matches) {
        const lines = content.split('\n');
        matches.forEach(match => {
          // 找到匹配的行号
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(match)) {
              // 排除注释和文档字符串中的匹配
              const line = lines[i].trim();
              if (!line.startsWith('//') && !line.startsWith('*') && !line.startsWith('/*')) {
                const issue = {
                  file: filePath,
                  line: i + 1,
                  match: match,
                  type: name,
                  severity: severity,
                  context: lines[i].trim()
                };

                // 检查是否在白名单中
                if (!isWhitelisted(issue, whitelist)) {
                  issues.push(issue);
                }
              }
              break;
            }
          }
        });
      }
    });

    return issues;
  } catch (error) {
    console.warn(`⚠️  无法读取文件: ${filePath}`);
    return [];
  }
}

function scanDirectory(dirPath, whitelist) {
  let allIssues = [];

  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        allIssues = allIssues.concat(scanDirectory(fullPath, whitelist));
      } else if (stat.isFile()) {
        const issues = checkFile(fullPath, whitelist);
        allIssues = allIssues.concat(issues);
      }
    }
  } catch (error) {
    console.warn(`⚠️  无法扫描目录: ${dirPath}`);
  }

  return allIssues;
}

// 开始扫描
const projectRoot = path.join(__dirname, '..');
const whitelist = loadWhitelist();

if (whitelist) {
  console.log('✅ 已加载白名单配置\n');
}

const issues = scanDirectory(projectRoot, whitelist);

// 按严重程度分组
const highSeverity = issues.filter(issue => issue.severity === 'high');
const mediumSeverity = issues.filter(issue => issue.severity === 'medium');

console.log('📊 扫描结果:\n');

if (issues.length === 0) {
  console.log('✅ 未发现需要修复的硬编码配置问题!');
  if (whitelist && whitelist.whitelist.acceptable_defaults) {
    console.log('📝 已忽略合理的默认值配置（这些值已通过环境变量支持）');
  }
} else {
  console.log(`❌ 发现 ${issues.length} 个需要修复的硬编码配置问题:\n`);

  if (highSeverity.length > 0) {
    console.log('🔴 高风险问题 (需要立即修复):');
    highSeverity.forEach(issue => {
      console.log(`   📁 ${issue.file}:${issue.line}`);
      console.log(`   🎯 ${issue.type}: ${issue.match}`);
      console.log(`   📝 ${issue.context}`);
      console.log('');
    });
  }

  if (mediumSeverity.length > 0) {
    console.log('🟡 中等风险问题 (建议修复):');
    mediumSeverity.forEach(issue => {
      console.log(`   📁 ${issue.file}:${issue.line}`);
      console.log(`   🎯 ${issue.type}: ${issue.match}`);
      console.log(`   📝 ${issue.context}`);
      console.log('');
    });
  }

  console.log('💡 修复建议:');
  console.log('1. 将硬编码的IP地址和端口移到环境变量中');
  console.log('2. 使用 .env 文件或环境变量来配置服务地址');
  console.log('3. 更新相关的配置文件和文档');
  console.log('4. 确保生产环境使用正确的配置值');
}

console.log('\n🔧 相关环境变量配置请参考 env.example 文件'); 