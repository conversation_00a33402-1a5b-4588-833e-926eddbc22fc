/**
 * 预加载功能测试工具
 * 用于验证应用预加载机制是否正常工作
 */

import appPreloader from '../services/AppPreloader';
import enhancedDocumentService from '../services/documentService';
import pageContentCollector from '../services/pageContentCollector';

class PreloadTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('[PreloadTest] 开始运行预加载测试...');
    
    const tests = [
      this.testAppPreloaderInitialization,
      this.testDocumentServiceStatus,
      this.testPageContentCollectorStatus,
      this.testPreloadTiming,
      this.testErrorHandling
    ];

    for (const test of tests) {
      try {
        await test.call(this);
      } catch (error) {
        console.error(`[PreloadTest] 测试失败: ${test.name}`, error);
        this.testResults.push({
          test: test.name,
          status: 'failed',
          error: error.message
        });
      }
    }

    this.printResults();
    return this.testResults;
  }

  /**
   * 测试AppPreloader初始化
   */
  async testAppPreloaderInitialization() {
    console.log('[PreloadTest] 测试AppPreloader初始化...');
    
    const startTime = Date.now();
    const status = await appPreloader.startPreload();
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    
    this.testResults.push({
      test: 'AppPreloader初始化',
      status: status.isComplete ? 'passed' : 'failed',
      duration: `${duration}ms`,
      details: {
        documentsLoaded: status.documentsLoaded,
        pageContentCollected: status.pageContentCollected,
        errors: status.errors
      }
    });

    console.log(`[PreloadTest] AppPreloader初始化完成，耗时: ${duration}ms`);
  }

  /**
   * 测试文档服务状态
   */
  async testDocumentServiceStatus() {
    console.log('[PreloadTest] 测试文档服务状态...');
    
    const loadingStatus = enhancedDocumentService.getLoadingStatus();
    const documentStats = enhancedDocumentService.getDocumentStats();
    
    this.testResults.push({
      test: '文档服务状态',
      status: loadingStatus.canSearch ? 'passed' : 'warning',
      details: {
        status: loadingStatus.status,
        message: loadingStatus.message,
        documentsCount: loadingStatus.documentsCount,
        totalDocuments: documentStats.totalDocuments,
        canSearch: loadingStatus.canSearch
      }
    });

    console.log(`[PreloadTest] 文档服务状态: ${loadingStatus.status}, 可搜索: ${loadingStatus.canSearch}`);
  }

  /**
   * 测试页面内容收集器状态
   */
  async testPageContentCollectorStatus() {
    console.log('[PreloadTest] 测试页面内容收集器状态...');
    
    const stats = pageContentCollector.getStats();
    const currentContent = pageContentCollector.getCurrentContent();
    
    this.testResults.push({
      test: '页面内容收集器状态',
      status: stats.isCollecting && stats.isInitialized ? 'passed' : 'warning',
      details: {
        isCollecting: stats.isCollecting,
        isInitialized: stats.isInitialized,
        hasContent: !!currentContent,
        cacheSize: stats.cacheSize,
        errorCount: stats.errorCount
      }
    });

    console.log(`[PreloadTest] 页面内容收集器: 初始化=${stats.isInitialized}, 收集中=${stats.isCollecting}`);
  }

  /**
   * 测试预加载时机
   */
  async testPreloadTiming() {
    console.log('[PreloadTest] 测试预加载时机...');
    
    const isComplete = appPreloader.isComplete();
    const preloadedData = appPreloader.getPreloadedData();
    
    this.testResults.push({
      test: '预加载时机',
      status: isComplete ? 'passed' : 'warning',
      details: {
        isComplete,
        hasDocuments: !!preloadedData.documents,
        hasPageContent: !!preloadedData.pageContent,
        hasDocumentStats: !!preloadedData.documentStats
      }
    });

    console.log(`[PreloadTest] 预加载完成: ${isComplete}`);
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('[PreloadTest] 测试错误处理...');
    
    const appStatus = appPreloader.getStatus();
    const docStatus = enhancedDocumentService.getLoadingStatus();
    
    const hasErrors = appStatus.errors.length > 0;
    const hasRecovery = docStatus.canSearch || docStatus.documentsCount > 0;
    
    this.testResults.push({
      test: '错误处理',
      status: hasErrors ? (hasRecovery ? 'warning' : 'failed') : 'passed',
      details: {
        appErrors: appStatus.errors,
        hasRecovery,
        canStillFunction: docStatus.canSearch
      }
    });

    console.log(`[PreloadTest] 错误处理: 有错误=${hasErrors}, 有恢复=${hasRecovery}`);
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n[PreloadTest] ===== 测试结果汇总 =====');
    
    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const warnings = this.testResults.filter(r => r.status === 'warning').length;
    const failed = this.testResults.filter(r => r.status === 'failed').length;
    
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passed} 个`);
    console.log(`警告: ${warnings} 个`);
    console.log(`失败: ${failed} 个`);
    
    this.testResults.forEach(result => {
      const icon = result.status === 'passed' ? '✅' : 
                   result.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      
      if (result.details) {
        console.log('   详情:', result.details);
      }
      
      if (result.error) {
        console.log('   错误:', result.error);
      }
    });
    
    console.log('=====================================\n');
  }

  /**
   * 获取测试摘要
   */
  getSummary() {
    const passed = this.testResults.filter(r => r.status === 'passed').length;
    const total = this.testResults.length;
    
    return {
      total,
      passed,
      warnings: this.testResults.filter(r => r.status === 'warning').length,
      failed: this.testResults.filter(r => r.status === 'failed').length,
      successRate: total > 0 ? (passed / total * 100).toFixed(1) : 0,
      overallStatus: passed === total ? 'excellent' : 
                     passed >= total * 0.8 ? 'good' : 
                     passed >= total * 0.6 ? 'fair' : 'poor'
    };
  }
}

// 创建全局测试实例
const preloadTest = new PreloadTest();

// 在开发环境中自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟运行测试，确保应用完全加载
  setTimeout(() => {
    preloadTest.runAllTests().then(() => {
      const summary = preloadTest.getSummary();
      console.log(`[PreloadTest] 整体评估: ${summary.overallStatus} (${summary.successRate}% 通过率)`);
    });
  }, 3000);
}

export default preloadTest;
