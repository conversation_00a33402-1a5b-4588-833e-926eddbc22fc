/**
 * 图片优化工具
 */

/**
 * 检测浏览器是否支持WebP格式
 */
export const supportsWebP = () => {
  if (typeof window === 'undefined') return false;
  
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;
    
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  } catch {
    return false;
  }
};

/**
 * 检测浏览器是否支持AVIF格式
 */
export const supportsAVIF = () => {
  if (typeof window === 'undefined') return false;
  
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
  } catch {
    return false;
  }
};

/**
 * 获取最佳图片格式
 */
export const getBestImageFormat = (originalSrc, formats = {}) => {
  const { webp, avif } = formats;
  
  // 优先级：AVIF > WebP > 原格式
  if (avif && supportsAVIF()) {
    return { src: avif, format: 'avif' };
  }
  
  if (webp && supportsWebP()) {
    return { src: webp, format: 'webp' };
  }
  
  return { src: originalSrc, format: 'original' };
};

/**
 * 生成响应式图片源集
 */
export const generateSrcSet = (baseSrc, sizes = [320, 640, 1024, 1920]) => {
  const extension = baseSrc.split('.').pop();
  const baseName = baseSrc.replace(`.${extension}`, '');
  
  return sizes
    .map(size => `${baseName}_${size}w.${extension} ${size}w`)
    .join(', ');
};

/**
 * 压缩图片
 */
export const compressImage = (file, quality = 0.8, maxWidth = 1920) => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // 计算压缩后的尺寸
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;
      
      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      // 转换为blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('压缩失败'));
          }
        },
        'image/jpeg',
        quality
      );
    };
    
    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 图片懒加载观察器
 */
export class LazyImageObserver {
  constructor(options = {}) {
    this.options = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    };
    
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      this.options
    );
    
    this.loadedImages = new Set();
  }
  
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target);
        this.observer.unobserve(entry.target);
      }
    });
  }
  
  loadImage(img) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;
    
    if (src && !this.loadedImages.has(src)) {
      // 预加载图片
      const tempImg = new Image();
      tempImg.onload = () => {
        img.src = src;
        if (srcset) img.srcset = srcset;
        img.classList.add('loaded');
        this.loadedImages.add(src);
      };
      tempImg.src = src;
    }
  }
  
  observe(img) {
    this.observer.observe(img);
  }
  
  unobserve(img) {
    this.observer.unobserve(img);
  }
  
  disconnect() {
    this.observer.disconnect();
    this.loadedImages.clear();
  }
}

/**
 * 创建全局懒加载观察器实例
 */
export const globalLazyImageObserver = new LazyImageObserver();

/**
 * 预加载关键图片
 */
export const preloadCriticalImages = (images = []) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'image';
  
  images.forEach(src => {
    const preloadLink = link.cloneNode();
    preloadLink.href = src;
    document.head.appendChild(preloadLink);
  });
};

/**
 * 图片性能监控
 */
export const ImagePerformanceMonitor = {
  metrics: {
    totalImages: 0,
    loadedImages: 0,
    failedImages: 0,
    averageLoadTime: 0,
    totalLoadTime: 0
  },
  
  startTiming(_src) {
    this.metrics.totalImages++;
    return performance.now();
  },
  
  endTiming(startTime, success = true) {
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    
    if (success) {
      this.metrics.loadedImages++;
      this.metrics.totalLoadTime += loadTime;
      this.metrics.averageLoadTime = this.metrics.totalLoadTime / this.metrics.loadedImages;
    } else {
      this.metrics.failedImages++;
    }
    
    return loadTime;
  },
  
  getMetrics() {
    return { ...this.metrics };
  },
  
  reset() {
    this.metrics = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      totalLoadTime: 0
    };
  }
}; 