#!/usr/bin/env node

/**
 * 构建后处理脚本：替换外部链接为本地资源
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔗 开始替换外部链接...');

// 外部链接映射
const linkReplacements = {
  // 当前无需替换的外部链接
};

// 递归处理目录中的文件
function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.js') || file.endsWith('.css') || file.endsWith('.html')) {
      processFile(filePath);
    }
  });
}

// 处理单个文件
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let modified = false;
    
    // 替换所有外部链接
    Object.entries(linkReplacements).forEach(([external, local]) => {
      const regex = new RegExp(external.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      if (content.includes(external)) {
        content = content.replace(regex, local);
        modified = true;
        console.log(`✅ 替换链接: ${path.basename(filePath)} - ${external} -> ${local}`);
      }
    });
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content);
    }
  } catch (error) {
    console.warn(`⚠️ 处理文件失败: ${filePath} - ${error.message}`);
  }
}

// 主处理逻辑
if (fs.existsSync('dist')) {
  console.log('📂 处理 dist 目录...');
  processDirectory('dist');
  
  // 验证替换结果
  console.log('\n🔍 验证外部链接替换结果...');
  if (Object.keys(linkReplacements).length > 0) {
    // 检查是否还有需要替换的外部链接
    const externalPatterns = Object.keys(linkReplacements).map(pattern => 
      pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    ).join('\\|');
    
    try {
      const result = execSync(`find dist -type f \\( -name "*.js" -o -name "*.css" -o -name "*.html" \\) -exec grep -l "${externalPatterns}" {} \\;`, 
        { encoding: 'utf-8' });
      
      if (result.trim()) {
        console.log('⚠️ 仍有文件包含外部链接:');
        console.log(result);
      } else {
        console.log('✅ 所有外部链接已成功替换');
      }
    } catch (error) {
      console.log('✅ 所有外部链接已成功替换');
    }
  } else {
    console.log('✅ 当前无需替换外部链接');
  }
  
  console.log('\n🎉 外部链接替换完成！');
} else {
  console.log('❌ dist 目录不存在，请先运行构建');
  process.exit(1);
}
