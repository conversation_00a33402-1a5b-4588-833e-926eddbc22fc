<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理缓存 - YNNX AI Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 清理缓存工具</h1>
        <p>如果网站无法正常加载，请使用以下工具清理缓存：</p>
        
        <div id="status" class="status">
            <strong>状态：</strong>准备清理
        </div>
        
        <div>
            <button class="btn" onclick="clearServiceWorker()">清理 Service Worker</button>
            <button class="btn" onclick="clearAllCaches()">清理所有缓存</button>
            <button class="btn" onclick="clearStorage()">清理本地存储</button>
            <button class="btn" onclick="fullCleanup()">完全清理</button>
        </div>
        
        <div>
            <button class="btn" onclick="window.location.href='/'">返回首页</button>
            <button class="btn" onclick="forceReload()">强制刷新</button>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 4px;">
            <h3>⚠️ 手动清理步骤：</h3>
            <ol>
                <li>按 <code>Ctrl+Shift+Delete</code> (或 <code>Cmd+Shift+Delete</code>)</li>
                <li>选择"所有时间"</li>
                <li>勾选"缓存的图片和文件"</li>
                <li>勾选"Cookie 和其他网站数据"</li>
                <li>点击"清除数据"</li>
                <li>重新访问网站</li>
            </ol>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const results = document.getElementById('results');
            
            status.innerHTML = `<strong>状态：</strong>${message}`;
            
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
        }

        async function clearServiceWorker() {
            updateStatus('正在清理 Service Worker...', 'info');
            
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                    
                    updateStatus(`已清理 ${registrations.length} 个 Service Worker`, 'success');
                } else {
                    updateStatus('浏览器不支持 Service Worker', 'error');
                }
            } catch (error) {
                updateStatus(`清理 Service Worker 失败: ${error.message}`, 'error');
            }
        }

        async function clearAllCaches() {
            updateStatus('正在清理所有缓存...', 'info');
            
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    
                    for (let cacheName of cacheNames) {
                        await caches.delete(cacheName);
                    }
                    
                    updateStatus(`已清理 ${cacheNames.length} 个缓存`, 'success');
                } else {
                    updateStatus('浏览器不支持 Cache API', 'error');
                }
            } catch (error) {
                updateStatus(`清理缓存失败: ${error.message}`, 'error');
            }
        }

        function clearStorage() {
            updateStatus('正在清理本地存储...', 'info');
            
            try {
                localStorage.clear();
                sessionStorage.clear();
                updateStatus('本地存储已清理', 'success');
            } catch (error) {
                updateStatus(`清理存储失败: ${error.message}`, 'error');
            }
        }

        async function fullCleanup() {
            updateStatus('开始完全清理...', 'info');
            
            await clearServiceWorker();
            await clearAllCaches();
            clearStorage();
            
            updateStatus('完全清理完成！', 'success');
            
            setTimeout(() => {
                updateStatus('3秒后自动刷新页面...', 'info');
                setTimeout(() => {
                    window.location.reload(true);
                }, 3000);
            }, 1000);
        }

        function forceReload() {
            updateStatus('强制刷新页面...', 'info');
            window.location.reload(true);
        }

        // 页面加载时检查状态
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，可以开始清理', 'success');
        });
    </script>
</body>
</html> 