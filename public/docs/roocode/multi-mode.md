# RooCode多模式使用指南

> RooCode四种工作模式详解：Code、Architect、Ask、Debug模式的特点和使用场景

## 🔨 Code模式

### 模式特点
Code模式是RooCode的核心编程模式，专注于具体的代码实现和开发任务。

**主要功能：**
- 智能代码补全和生成
- 文件创建、编辑和重构
- 自动化测试生成
- 错误检测和修复
- 终端命令执行
- 浏览器自动化操作

### 适用场景
- 日常编程开发任务
- 功能模块实现
- 代码维护和优化
- 单元测试编写
- 调试和问题修复

### 使用示例
```javascript
// 用户输入：创建一个用户登录组件
// RooCode会生成完整的组件代码

import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';

const LoginForm = () => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const { login, loading } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    await login(credentials);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
};

export default LoginForm;
```

![Code模式界面](./images/roocode-code-mode.png)

## 🏗️ Architect模式

### 模式特点
Architect模式专注于系统架构设计和技术决策，不涉及具体的代码实现。

**主要功能：**
- 系统架构设计建议
- 技术选型指导
- 模块依赖分析
- 性能优化建议
- 安全性评估
- 可扩展性规划

### 限制说明
- **不能编写代码**：仅提供设计建议
- **不能执行命令**：专注于架构思考
- **不能操作文件**：避免过早实现

### 适用场景
- 新项目架构设计
- 技术栈选择
- 系统重构规划
- 性能瓶颈分析
- 团队技术决策

### 架构设计示例
```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[数据存储层]
    
    subgraph "前端架构"
        A1[React组件]
        A2[状态管理]
        A3[路由系统]
    end
    
    subgraph "后端架构"
        B1[API网关]
        B2[微服务]
        B3[消息队列]
    end
```

![Architect模式分析](./images/roocode-architect-mode.png)

## ❓ Ask模式

### 模式特点
Ask模式专注于技术咨询和知识传授，类似于一个专业的技术顾问。

**主要功能：**
- 编程概念解释
- 最佳实践推荐
- 技术原理阐述
- 学习路径指导
- 代码审查建议
- 工具和框架比较

### 限制说明
- **不能编写代码**：专注于知识传授
- **不能执行命令**：避免干扰学习过程
- **不能操作文件**：保持纯粹的咨询角色

### 适用场景
- 学习新技术和框架
- 理解复杂的编程概念
- 获取最佳实践建议
- 解决技术疑问
- 代码设计思路讨论

### 咨询示例对话
```
用户：什么是React Hooks？为什么要使用它们？

RooCode Ask模式回答：
React Hooks是React 16.8引入的新特性，它们是一些特殊的函数，
让你能在函数组件中使用state和其他React特性。

主要优势：
1. 代码复用：自定义Hook可以在组件间共享逻辑
2. 更简洁：避免类组件的复杂性
3. 更好的性能：减少不必要的渲染
4. 更容易测试：函数组件更容易进行单元测试

常用的内置Hooks：
- useState：管理组件状态
- useEffect：处理副作用
- useContext：访问React Context
- useMemo：性能优化
- useCallback：缓存函数引用
```

## 🐛 Debug模式

### 模式特点
Debug模式专门用于系统化的问题诊断和调试，采用结构化的方法解决问题。

**主要功能：**
- 错误分析和诊断
- 性能问题定位
- 日志分析和解读
- 调试策略制定
- 修复方案推荐
- 预防措施建议

### 调试方法论
1. **问题识别**：准确描述问题现象
2. **信息收集**：收集相关日志和错误信息
3. **原因分析**：系统性分析可能原因
4. **解决方案**：提供多种修复方案
5. **验证测试**：确认问题是否解决
6. **预防措施**：避免问题再次发生

### 适用场景
- Bug修复和问题排查
- 性能优化和调试
- 系统故障分析
- 代码质量问题
- 集成测试问题

### 调试示例
```
问题报告：React应用在生产环境中加载缓慢

Debug模式分析过程：

1. 问题识别
   - 症状：首次加载时间超过10秒
   - 环境：生产环境，开发环境正常
   - 影响范围：所有用户

2. 可能原因分析
   a) 网络问题：CDN配置、DNS解析
   b) 代码问题：bundle大小、代码分割
   c) 服务器问题：响应时间、缓存策略
   d) 浏览器问题：兼容性、缓存

3. 诊断步骤
   - 检查Network面板的资源加载时间
   - 分析bundle analyzer报告
   - 测试不同网络环境
   - 检查服务器响应时间

4. 解决方案
   - 启用代码分割和懒加载
   - 优化图片和静态资源
   - 配置适当的缓存策略
   - 使用服务端渲染(SSR)
```

![Debug模式界面](./images/roocode-debug-mode.png)

## 🔄 模式切换与配置

### 快速切换
在RooCode聊天界面的输入框左下角有模式选择下拉菜单，可以快速切换：
- 点击下拉箭头
- 选择需要的模式
- 立即切换到新模式

### 独立配置
每个模式可以配置不同的API提供商和模型：

```json
{
  "roocode.modes": {
    "code": {
      "provider": "openai-compatible",
      "model": "deepseek-v3",
      "temperature": 0.1
    },
    "architect": {
      "provider": "openai-compatible", 
      "model": "gemini-2.5-flash",
      "temperature": 0.3
    },
    "ask": {
      "provider": "openai-compatible",
      "model": "qwen3-235b-a22b",
      "temperature": 0.2
    },
    "debug": {
      "provider": "openai-compatible",
      "model": "claude-3.5-sonnet",
      "temperature": 0.1
    }
  }
}
```

### 模型建议
根据不同的任务特性，推荐使用不同的模型：

| 模式 | 推荐模型 | 原因 |
|------|---------|------|
| Code | DeepSeek V3 | 代码生成能力强，理解力好 |
| Architect | Gemini 2.5 | 逻辑推理和架构设计能力出色 |
| Ask | qwen3-235b-a22b | 知识面广，解释能力强 |
| Debug | Claude 3.5 Sonnet | 分析能力强，调试思路清晰 |

## 🎯 自定义模式

### 创建专门角色
可以基于现有模式创建专门的角色：

#### UI/UX设计师模式
基于Ask模式，专注于界面设计咨询：
```json
{
  "roocode.customModes": {
    "uiux": {
      "baseMode": "ask",
      "name": "UI/UX设计师",
      "systemPrompt": "你是一个专业的UI/UX设计师...",
      "model": "gemini-2.5-flash"
    }
  }
}
```

#### 性能优化专家模式
基于Debug模式，专注于性能问题：
```json
{
  "performance": {
    "baseMode": "debug",
    "name": "性能优化专家",
    "systemPrompt": "你是一个前端性能优化专家...",
    "model": "claude-3.5-sonnet"
  }
}
```

## 📋 模式记忆功能

RooCode会记住每个模式的使用偏好：
- **最后使用的配置**：自动恢复上次的API配置
- **常用提示词**：保存frequently used prompts
- **项目上下文**：记住项目特定的配置

## 📹 多模式演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/roocode-multi-mode-demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## 🏆 最佳实践

### 模式选择指南
1. **开发新功能** → Code模式
2. **项目规划** → Architect模式  
3. **学习技术** → Ask模式
4. **解决问题** → Debug模式

### 工作流建议
1. **需求分析阶段**：Ask模式了解技术要求
2. **架构设计阶段**：Architect模式制定技术方案
3. **开发实现阶段**：Code模式编写具体代码
4. **问题解决阶段**：Debug模式处理bug和性能问题

### 效率提升技巧
- 为不同项目类型创建模式模板
- 使用快捷键快速切换模式
- 配置模式特定的提示词库
- 建立团队模式使用规范

---

*最后更新时间：2024年12月* 