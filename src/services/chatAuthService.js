// LobeChat认证集成服务
// 负责将平台的LDAP认证与LobeChat的next-auth系统集成

class ChatAuthService {
  constructor() {
    this.STORAGE_KEY_CHAT_TOKEN = 'ynnxChatToken';
    this.STORAGE_KEY_CHAT_SESSION = 'ynnxChatSession';
    this.PLATFORM_AUTH_HEADER = 'X-Platform-Auth';
    this.PLATFORM_TOKEN_HEADER = 'X-Platform-Token';
  }

  /**
   * 为用户生成LobeChat认证令牌
   * @param {Object} userData - 用户数据
   * @returns {Object} 认证令牌信息
   */
  generateChatAuthToken(userData) {
    if (!userData) {
      throw new Error('用户数据不能为空');
    }

    const timestamp = new Date().getTime();
    const tokenData = {
      userId: userData.username || userData.name,
      userName: userData.name || userData.username,
      userEmail: userData.email || `${userData.username}@ynnx.com`,
      platform: 'ynnx-platform',
      ldapEnvironment: userData.ldapServer || 'DEVVDI_ENV',
      issuedAt: timestamp,
      expiresAt: timestamp + (24 * 60 * 60 * 1000), // 24小时过期
      sessionId: this.generateSessionId(userData.username, timestamp)
    };

    // 生成简单的签名（在生产环境中应使用更安全的方法）
    const signature = this.generateSignature(tokenData);
    
    const authToken = {
      ...tokenData,
      signature
    };

    // 存储到localStorage
    localStorage.setItem(this.STORAGE_KEY_CHAT_TOKEN, JSON.stringify(authToken));
    
    console.log('[ChatAuth] 为用户生成认证令牌:', userData.username);
    return authToken;
  }

  /**
   * 获取当前用户的LobeChat认证令牌
   * @returns {Object|null} 认证令牌或null
   */
  getChatAuthToken() {
    try {
      const tokenStr = localStorage.getItem(this.STORAGE_KEY_CHAT_TOKEN);
      if (!tokenStr) {
        return null;
      }

      const token = JSON.parse(tokenStr);
      
      // 检查令牌是否过期
      if (token.expiresAt && new Date().getTime() > token.expiresAt) {
        console.log('[ChatAuth] 认证令牌已过期');
        this.clearChatAuth();
        return null;
      }

      return token;
    } catch (error) {
      console.error('[ChatAuth] 解析认证令牌失败:', error);
      return null;
    }
  }

  /**
   * 验证认证令牌的有效性
   * @param {Object} token - 认证令牌
   * @returns {boolean} 是否有效
   */
  validateAuthToken(token) {
    if (!token || !token.signature) {
      return false;
    }

    // 重新计算签名并验证
    const expectedSignature = this.generateSignature({
      userId: token.userId,
      userName: token.userName,
      userEmail: token.userEmail,
      platform: token.platform,
      ldapEnvironment: token.ldapEnvironment,
      issuedAt: token.issuedAt,
      expiresAt: token.expiresAt,
      sessionId: token.sessionId
    });

    return expectedSignature === token.signature;
  }

  /**
   * 生成会话ID
   * @param {string} username - 用户名
   * @param {number} timestamp - 时间戳
   * @returns {string} 会话ID
   */
  generateSessionId(username, timestamp) {
    const data = `${username}-${timestamp}-${Math.random()}`;
    return btoa(data).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32);
  }

  /**
   * 生成简单签名（生产环境应使用更安全的方法）
   * @param {Object} data - 要签名的数据
   * @returns {string} 签名
   */
  generateSignature(data) {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 设置LobeChat会话信息
   * @param {Object} userData - 用户数据
   */
  setChatSession(userData) {
    const authToken = this.generateChatAuthToken(userData);
    
    // 创建会话数据
    const sessionData = {
      user: {
        id: authToken.userId,
        name: authToken.userName,
        email: authToken.userEmail,
        image: null, // 可以后续添加头像支持
        platform: authToken.platform
      },
      expires: new Date(authToken.expiresAt).toISOString(),
      sessionToken: authToken.sessionId,
      accessToken: authToken.signature
    };

    // 存储会话数据
    localStorage.setItem(this.STORAGE_KEY_CHAT_SESSION, JSON.stringify(sessionData));
    
    // 设置认证头部信息供nginx代理使用
    this.setAuthHeaders(authToken);
    
    console.log('[ChatAuth] LobeChat会话已设置');
    return sessionData;
  }

  /**
   * 设置认证头部信息
   * @param {Object} authToken - 认证令牌
   */
  setAuthHeaders(authToken) {
    // 将认证信息存储到sessionStorage，供前端使用
    const authHeaders = {
      'X-Auth-User': authToken.userId,
      'X-Auth-Email': authToken.userEmail,
      'X-Auth-Name': authToken.userName,
      'X-Platform-Auth': authToken.platform,
      'X-Platform-Token': authToken.signature,
      'X-Session-Id': authToken.sessionId
    };

    sessionStorage.setItem('ynnx-chat-auth-headers', JSON.stringify(authHeaders));
    
    // 设置到document的meta标签中，供nginx读取
    Object.entries(authHeaders).forEach(([key, value]) => {
      let meta = document.querySelector(`meta[name="${key}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.name = key;
        document.head.appendChild(meta);
      }
      meta.content = value;
    });
  }

  /**
   * 获取LobeChat会话信息
   * @returns {Object|null} 会话数据或null
   */
  getChatSession() {
    try {
      const sessionStr = localStorage.getItem(this.STORAGE_KEY_CHAT_SESSION);
      if (!sessionStr) {
        return null;
      }

      const session = JSON.parse(sessionStr);
      
      // 检查会话是否过期
      if (session.expires && new Date() > new Date(session.expires)) {
        console.log('[ChatAuth] LobeChat会话已过期');
        this.clearChatAuth();
        return null;
      }

      return session;
    } catch (error) {
      console.error('[ChatAuth] 解析会话数据失败:', error);
      return null;
    }
  }

  /**
   * 清除LobeChat认证信息
   */
  clearChatAuth() {
    localStorage.removeItem(this.STORAGE_KEY_CHAT_TOKEN);
    localStorage.removeItem(this.STORAGE_KEY_CHAT_SESSION);
    sessionStorage.removeItem('ynnx-chat-auth-headers');
    
    // 清除meta标签
    const authHeaders = ['X-Auth-User', 'X-Auth-Email', 'X-Auth-Name', 'X-Platform-Auth', 'X-Platform-Token', 'X-Session-Id'];
    authHeaders.forEach(header => {
      const meta = document.querySelector(`meta[name="${header}"]`);
      if (meta) {
        meta.remove();
      }
    });
    
    console.log('[ChatAuth] LobeChat认证信息已清除');
  }

  /**
   * 检查用户是否有LobeChat访问权限
   * @param {Object} userData - 用户数据
   * @returns {boolean} 是否有权限
   */
  hasAccessPermission(userData) {
    // 基本权限检查：用户必须已登录
    if (!userData || !userData.username) {
      return false;
    }

    // 可以在这里添加更多的权限检查逻辑
    // 例如：检查用户角色、部门等
    
    return true;
  }

  /**
   * 刷新认证令牌
   * @param {Object} userData - 用户数据
   * @returns {Object|null} 新的认证令牌
   */
  refreshAuthToken(userData) {
    if (!userData) {
      return null;
    }

    // 清除旧的认证信息
    this.clearChatAuth();
    
    // 生成新的认证令牌
    return this.setChatSession(userData);
  }

  /**
   * 获取认证状态信息
   * @returns {Object} 认证状态
   */
  getAuthStatus() {
    const token = this.getChatAuthToken();
    const session = this.getChatSession();
    
    return {
      isAuthenticated: token !== null && session !== null,
      token,
      session,
      hasValidToken: token !== null && this.validateAuthToken(token),
      expiresAt: token ? new Date(token.expiresAt) : null
    };
  }
}

// 创建单例实例
const chatAuthService = new ChatAuthService();

export default chatAuthService;
