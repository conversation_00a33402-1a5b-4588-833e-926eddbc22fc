<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问题路由测试 - 工具下载页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8fafc;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .download-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .tool-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            background: #f8fafc;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .tool-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .tool-description {
            color: #4a5568;
            margin-bottom: 12px;
            font-size: 0.9em;
        }
        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.8em;
            color: #718096;
        }
        .download-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
            width: 100%;
        }
        .download-btn:hover {
            background: #3182ce;
        }
        .download-btn.disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
        .category-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }
        .test-questions {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .question-example {
            background: #edf2f7;
            border-left: 4px solid #4299e1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 6px 6px 0;
        }
        .question-type {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        .question-text {
            color: #4a5568;
            font-style: italic;
        }
        .expected-behavior {
            font-size: 0.85em;
            color: #718096;
            margin-top: 5px;
        }
        .platform-badge {
            background: #e2e8f0;
            color: #4a5568;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            margin-right: 5px;
        }
        .version-badge {
            background: #c6f6d5;
            color: #22543d;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ 开发工具下载中心</h1>
        <p>为开发者提供最新的工具和软件下载</p>
    </div>

    <!-- 开发工具分类 -->
    <div class="download-section">
        <div class="category-title">🔧 开发工具</div>
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-name">Cline VS Code插件</div>
                <div class="tool-description">强大的AI编程助手，支持代码生成、重构和调试</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">VS Code</span>
                        <span class="version-badge">v2.1.0</span>
                    </div>
                    <div>大小: 15.2MB</div>
                </div>
                <button class="download-btn" data-download="cline-vscode" data-url="/downloads/cline-vscode-2.1.0.vsix">
                    下载 Cline 插件
                </button>
            </div>

            <div class="tool-card">
                <div class="tool-name">JetBrains AI Assistant</div>
                <div class="tool-description">JetBrains IDE的AI编程助手插件</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">IntelliJ</span>
                        <span class="platform-badge">PyCharm</span>
                        <span class="version-badge">v1.8.5</span>
                    </div>
                    <div>大小: 28.7MB</div>
                </div>
                <button class="download-btn" data-download="jetbrains-ai" data-url="/downloads/jetbrains-ai-1.8.5.zip">
                    下载 JetBrains AI
                </button>
            </div>

            <div class="tool-card">
                <div class="tool-name">RooCode 编辑器</div>
                <div class="tool-description">轻量级代码编辑器，支持多种编程语言</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">Windows</span>
                        <span class="platform-badge">macOS</span>
                        <span class="platform-badge">Linux</span>
                        <span class="version-badge">v3.2.1</span>
                    </div>
                    <div>大小: 45.8MB</div>
                </div>
                <button class="download-btn" data-download="roocode" data-url="/downloads/roocode-3.2.1.exe">
                    下载 RooCode
                </button>
            </div>
        </div>
    </div>

    <!-- 软件应用分类 -->
    <div class="download-section">
        <div class="category-title">💻 软件应用</div>
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-name">Docker Desktop</div>
                <div class="tool-description">容器化应用开发和部署平台</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">Windows</span>
                        <span class="platform-badge">macOS</span>
                        <span class="version-badge">v4.15.0</span>
                    </div>
                    <div>大小: 512MB</div>
                </div>
                <button class="download-btn" data-download="docker" data-url="/downloads/docker-desktop-4.15.0.exe">
                    下载 Docker Desktop
                </button>
            </div>

            <div class="tool-card">
                <div class="tool-name">Git for Windows</div>
                <div class="tool-description">Windows平台的Git版本控制工具</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">Windows</span>
                        <span class="version-badge">v2.42.0</span>
                    </div>
                    <div>大小: 47.3MB</div>
                </div>
                <button class="download-btn disabled" disabled data-download="git" data-url="/downloads/git-2.42.0.exe">
                    暂时不可用
                </button>
            </div>
        </div>
    </div>

    <!-- 文档资源分类 -->
    <div class="download-section">
        <div class="category-title">📚 文档资源</div>
        <div class="tools-grid">
            <div class="tool-card">
                <div class="tool-name">开发工具使用指南</div>
                <div class="tool-description">详细的工具安装和配置指南</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">PDF</span>
                        <span class="version-badge">v2023.1</span>
                    </div>
                    <div>大小: 8.5MB</div>
                </div>
                <a href="/downloads/dev-tools-guide-2023.pdf" class="download-btn" style="text-decoration: none; display: block; text-align: center;">
                    下载使用指南
                </a>
            </div>

            <div class="tool-card">
                <div class="tool-name">API 参考手册</div>
                <div class="tool-description">完整的API接口文档和示例</div>
                <div class="tool-meta">
                    <div>
                        <span class="platform-badge">PDF</span>
                        <span class="version-badge">v1.2.0</span>
                    </div>
                    <div>大小: 12.1MB</div>
                </div>
                <a href="/downloads/api-reference-1.2.0.pdf" class="download-btn" style="text-decoration: none; display: block; text-align: center;">
                    下载API手册
                </a>
            </div>
        </div>
    </div>

    <!-- 测试问题示例 -->
    <div class="test-questions">
        <h2>🧪 智能问题路由测试</h2>
        <p>以下是不同类型问题的示例，用于测试AI助手的智能路由功能：</p>

        <div class="question-example">
            <div class="question-type">页面内容类问题 (应使用页面分析)</div>
            <div class="question-text">"工具下载有什么？"</div>
            <div class="question-text">"页面上显示了哪些工具？"</div>
            <div class="question-text">"这里有什么下载选项？"</div>
            <div class="expected-behavior">期望: 使用页面分析，返回当前页面的具体工具列表</div>
        </div>

        <div class="question-example">
            <div class="question-type">文档搜索类问题 (应使用文档搜索)</div>
            <div class="question-text">"如何安装Cline插件？"</div>
            <div class="question-text">"Docker配置教程在哪里？"</div>
            <div class="question-text">"Git使用方法说明"</div>
            <div class="expected-behavior">期望: 使用文档搜索，返回相关的安装和配置指南</div>
        </div>

        <div class="question-example">
            <div class="question-type">混合类问题 (应同时使用两种方式)</div>
            <div class="question-text">"详细介绍页面上的开发工具"</div>
            <div class="question-text">"完整说明这些工具的使用方法"</div>
            <div class="expected-behavior">期望: 结合页面内容和文档信息，提供全面的回答</div>
        </div>
    </div>

    <script>
        // 模拟下载功能
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.disabled) return;
                
                const toolName = this.textContent.trim();
                const downloadUrl = this.getAttribute('data-url');
                
                console.log(`模拟下载: ${toolName} from ${downloadUrl}`);
                
                // 显示下载提示
                const originalText = this.textContent;
                this.textContent = '下载中...';
                this.disabled = true;
                
                setTimeout(() => {
                    this.textContent = '下载完成 ✓';
                    setTimeout(() => {
                        this.textContent = originalText;
                        this.disabled = false;
                    }, 2000);
                }, 1500);
            });
        });

        // 添加页面加载完成标记
        window.addEventListener('load', () => {
            console.log('工具下载页面加载完成，包含以下内容:');
            console.log('- 开发工具: Cline, JetBrains AI, RooCode');
            console.log('- 软件应用: Docker Desktop, Git');
            console.log('- 文档资源: 使用指南, API手册');
        });
    </script>
</body>
</html>
