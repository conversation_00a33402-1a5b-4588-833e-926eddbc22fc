#!/usr/bin/env node

/**
 * 外部依赖检查脚本
 * 检查项目中的外部链接和依赖，验证内网部署准备情况
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 检查外部依赖...\n');

// 检查结果统计
let checkResults = {
  externalLinks: [],
  localResources: [],
  recommendations: []
};

// 1. 检查源码中的外部链接
console.log('📂 检查源码中的外部链接...');
try {
  const sourceFiles = [
    './src',
    './public', 
    './index.html'
  ].filter(p => fs.existsSync(p));

  if (sourceFiles.length > 0) {
    const result = execSync(
      `find ${sourceFiles.join(' ')} -type f \\( -name "*.js" -o -name "*.jsx" -o -name "*.css" -o -name "*.html" \\) -exec grep -l "https://" {} \\;`,
      {encoding: 'utf-8'}
    );
    
    if (result.trim()) {
      console.log('⚠️  发现包含外部链接的文件:');
      const files = result.trim().split('\n');
      
      files.forEach(file => {
        try {
          const matches = execSync(`grep -n "https://" "${file}"`, {encoding: 'utf-8'});
          console.log(`\n📄 ${file}:`);
          console.log(matches);
          checkResults.externalLinks.push({file, matches: matches.trim()});
        } catch (e) {
          // 文件可能被删除或无权限
        }
      });
    } else {
      console.log('✅ 源码中未发现外部链接');
    }
  }
} catch (error) {
  console.log('✅ 源码中未发现外部链接');
}

// 2. 检查构建产物
console.log('\n📦 检查构建产物...');
if (fs.existsSync('dist')) {
  try {
    const distResult = execSync('find ./dist -type f -exec grep -l "https://" {} \\;', {encoding: 'utf-8'});
    if (distResult.trim()) {
      console.log('⚠️  构建产物中发现外部链接:');
      console.log(distResult);
      checkResults.externalLinks.push({file: 'dist/*', matches: distResult.trim()});
    } else {
      console.log('✅ 构建产物无外部链接');
    }
  } catch (error) {
    console.log('✅ 构建产物无外部链接');
  }
} else {
  console.log('💡 未找到构建产物目录 (dist)');
  console.log('   运行 npm run build 后再次检查');
}

// 3. 检查字体和静态资源
console.log('\n🔤 检查字体和静态资源...');
const fontDirs = ['public/assets/fonts', 'public/assets/webfonts'];
const staticDirs = ['public/assets', 'src/assets'];

fontDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const files = fs.readdirSync(dir);
    if (files.length > 0) {
      console.log(`✅ 本地字体资源: ${dir} (${files.length} 个文件)`);
      checkResults.localResources.push({type: '字体', path: dir, count: files.length});
    }
  }
});

staticDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const files = execSync(`find ${dir} -type f | wc -l`, {encoding: 'utf-8'}).trim();
    console.log(`✅ 本地静态资源: ${dir} (${files} 个文件)`);
    checkResults.localResources.push({type: '静态资源', path: dir, count: parseInt(files)});
  }
});

// 4. 检查package.json依赖
console.log('\n📋 检查npm依赖管理...');
if (fs.existsSync('package.json')) {
  const packageData = JSON.parse(fs.readFileSync('package.json', 'utf-8'));
  const depsCount = Object.keys(packageData.dependencies || {}).length;
  const devDepsCount = Object.keys(packageData.devDependencies || {}).length;
  
  console.log(`✅ npm依赖管理: ${depsCount} 个运行时依赖, ${devDepsCount} 个开发依赖`);
  checkResults.localResources.push({
    type: 'npm依赖',
    path: 'package.json',
    count: depsCount + devDepsCount
  });
}

// 5. 检查Service Worker和PWA配置
console.log('\n🔧 检查PWA和Service Worker...');
if (fs.existsSync('public/sw.js')) {
  console.log('✅ Service Worker: 本地文件');
  checkResults.localResources.push({type: 'Service Worker', path: 'public/sw.js', count: 1});
}

if (fs.existsSync('public/manifest.json')) {
  console.log('✅ PWA Manifest: 本地文件');
  checkResults.localResources.push({type: 'PWA Manifest', path: 'public/manifest.json', count: 1});
}

// 6. 生成内网部署建议
console.log('\n🎯 内网部署建议:');

if (checkResults.externalLinks.length === 0) {
  console.log('🎉 恭喜！项目已完全适配内网环境');
  checkResults.recommendations.push('项目已准备就绪，可直接部署');
} else {
  console.log('⚠️  需要处理外部链接依赖');
  checkResults.recommendations.push('运行内网优化脚本: node scripts/prepare-intranet-deployment.cjs');
}

// 静态资源检查
const hasLocalFonts = checkResults.localResources.some(r => r.type === '字体');
const hasLocalAssets = checkResults.localResources.some(r => r.type === '静态资源');

console.log(`• 字体资源: ${hasLocalFonts ? '已本地化 ✅' : '需要检查 ⚠️'}`);
console.log(`• 静态资源: ${hasLocalAssets ? '已本地化 ✅' : '需要检查 ⚠️'}`);
console.log('• JavaScript依赖: npm管理 ✅');
console.log('• API调用: 需配置内网地址 💡');

// 7. 生成详细报告
const reportData = {
  timestamp: new Date().toISOString(),
  summary: {
    ready: checkResults.externalLinks.length === 0,
    externalLinksCount: checkResults.externalLinks.length,
    localResourcesCount: checkResults.localResources.length
  },
  externalLinks: checkResults.externalLinks,
  localResources: checkResults.localResources,
  recommendations: checkResults.recommendations
};

// 保存报告
const reportPath = 'intranet-deployment-report.json';
fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
console.log(`\n📊 详细报告已保存到: ${reportPath}`);

// 8. 显示快速操作指南
console.log('\n⚡ 快速操作指南:');

if (checkResults.externalLinks.length > 0) {
  console.log('1. 🔧 运行内网优化: node scripts/prepare-intranet-deployment.cjs');
  console.log('2. ⚙️  配置内网地址: 编辑 .env.intranet');
  console.log('3. 🏗️  构建项目: npm run build');
  console.log('4. ✅ 再次检查: npm run check:external');
} else {
  console.log('1. ⚙️  配置内网地址: 复制 env.example 为 .env 并修改');
  console.log('2. 🏗️  构建项目: npm run build');
  console.log('3. 🚀 部署到内网服务器');
}

console.log('5. 📖 查看完整指南: 查看 INTRANET_DEPLOYMENT.md');

// 9. 总结
console.log('\n📈 检查总结:');
console.log(`• 外部链接: ${checkResults.externalLinks.length} 个`);
console.log(`• 本地资源: ${checkResults.localResources.length} 类`);
console.log(`• 内网就绪: ${checkResults.externalLinks.length === 0 ? '是 ✅' : '否 ⚠️'}`);

if (checkResults.externalLinks.length === 0) {
  console.log('\n🎉 您的项目已完美适配内网环境！');
} else {
  console.log('\n💡 运行优化脚本后即可完成内网适配！');
} 