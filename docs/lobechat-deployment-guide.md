# LobeChat 集成部署指南

## 概述

本指南详细说明如何将 LobeChat 智能对话系统集成到 YNNX AI 平台中，实现单点登录(SSO)和用户会话隔离。

## 前置条件

1. YNNX AI 平台已正常运行
2. Docker 和 Docker Compose 已安装
3. nginx 反向代理已配置
4. LDAP 认证系统正常工作

## 部署步骤

### 1. 部署 LobeChat 服务

#### 方式一：使用 Docker 部署

```bash
# 创建 LobeChat 目录
mkdir -p /opt/lobechat
cd /opt/lobechat

# 创建 docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  lobechat:
    image: lobehub/lobe-chat-database:latest
    container_name: lobechat
    restart: unless-stopped
    ports:
      - "3210:3210"
    environment:
      # 基础配置
      - NODE_ENV=production
      - NEXT_PUBLIC_SERVICE_MODE=server
      - NEXT_PUBLIC_ENABLE_NEXT_AUTH=1
      
      # 认证配置
      - NEXT_AUTH_SECRET=your-generated-secret-key-here
      - NEXTAUTH_URL=http://***********:3210/api/auth
      - NEXT_AUTH_SSO_PROVIDERS=generic-oidc
      
      # 自定义认证提供者（接收平台认证）
      - AUTH_GENERIC_OIDC_ID=ynnx-platform
      - AUTH_GENERIC_OIDC_SECRET=ynnx-chat-token-2025
      - AUTH_GENERIC_OIDC_ISSUER=http://***********/api/auth
      
      # 应用配置
      - APP_URL=http://***********:3210
      
      # 数据库配置（使用 SQLite）
      - DATABASE_URL=file:./data/lobechat.db
      
      # 其他配置
      - ENABLE_AUTH_PROTECTION=1
      
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - lobechat-network

networks:
  lobechat-network:
    driver: bridge
EOF

# 生成认证密钥
echo "生成 NEXT_AUTH_SECRET..."
SECRET=$(openssl rand -base64 32)
sed -i "s/your-generated-secret-key-here/$SECRET/g" docker-compose.yml

# 启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f lobechat
```

#### 方式二：直接运行（如果已有 LobeChat 实例）

确保 LobeChat 运行在 `http://***********:3210` 并配置相应的环境变量。

### 2. 更新 YNNX 平台配置

#### 重新构建平台

```bash
# 进入平台目录
cd /path/to/ynnx-aidev-platform

# 停止现有服务
docker-compose down -v

# 构建新版本
npm run build

# 重新构建容器
docker-compose build --no-cache

# 启动服务
docker-compose up -d
```

### 3. 验证集成

#### 运行集成测试

```bash
# 运行测试脚本
node scripts/test-lobechat-integration.js
```

#### 手动验证

1. **访问平台**: 打开 `https://***********`
2. **用户登录**: 使用 LDAP 账户登录
3. **访问聊天**: 点击导航栏中的"智能对话"
4. **验证功能**: 确认 LobeChat 正常加载并可以对话

### 4. 配置检查清单

#### nginx 配置检查

```bash
# 检查 nginx 配置语法
nginx -t

# 重新加载 nginx 配置
nginx -s reload

# 查看 nginx 日志
tail -f /var/log/nginx/access.log | grep "/chat/"
```

#### 服务状态检查

```bash
# 检查 LobeChat 服务
curl -I http://***********:3210

# 检查代理路径
curl -I https://***********/chat/

# 检查平台服务
docker-compose ps
```

## 故障排除

### 常见问题及解决方案

#### 1. LobeChat 无法访问

**症状**: 浏览器显示连接错误或 502 错误

**解决方案**:
```bash
# 检查 LobeChat 服务状态
docker ps | grep lobechat
docker logs lobechat

# 检查端口占用
netstat -tlnp | grep 3210

# 重启 LobeChat 服务
docker-compose restart lobechat
```

#### 2. 认证失败

**症状**: 用户无法登录 LobeChat 或显示认证错误

**解决方案**:
```bash
# 检查认证配置
grep -r "AUTH_" .env*

# 查看平台认证日志
docker-compose logs app | grep -i auth

# 检查 nginx 头部传递
curl -H "X-Auth-User: testuser" https://***********/chat/
```

#### 3. iframe 加载问题

**症状**: LobeChat 在 iframe 中无法正常显示

**解决方案**:
```bash
# 检查 X-Frame-Options 头部
curl -I https://***********/chat/

# 查看浏览器控制台错误
# 检查 CSP 策略设置
```

#### 4. WebSocket 连接失败

**症状**: 实时功能不工作，连接超时

**解决方案**:
```bash
# 检查 nginx WebSocket 配置
grep -A 10 "Upgrade" nginx/conf.d/default.conf

# 测试 WebSocket 连接
wscat -c ws://***********:3210
```

### 日志分析

#### 查看关键日志

```bash
# 平台日志
docker-compose logs -f app

# nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# LobeChat 日志
docker logs -f lobechat

# 系统日志
journalctl -u docker -f
```

## 安全配置

### 1. 防火墙设置

```bash
# 只允许内网访问 LobeChat 直接端口
ufw deny 3210
ufw allow from 192.168.1.0/24 to any port 3210

# 确保 HTTPS 端口开放
ufw allow 443
```

### 2. SSL 证书

确保平台使用有效的 SSL 证书，避免混合内容问题。

### 3. 认证安全

- 定期轮换 `NEXT_AUTH_SECRET`
- 监控认证失败日志
- 实施访问频率限制

## 性能优化

### 1. 缓存配置

在 nginx 中添加静态资源缓存：

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 2. 连接池优化

调整 nginx 和 LobeChat 的连接参数：

```nginx
upstream lobechat_backend {
    server ***********:3210 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

### 3. 资源限制

为 LobeChat 容器设置资源限制：

```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1.0'
    reservations:
      memory: 1G
      cpus: '0.5'
```

## 监控和维护

### 1. 健康检查

添加健康检查端点监控：

```bash
# 创建监控脚本
cat > /opt/scripts/check-lobechat.sh << 'EOF'
#!/bin/bash
if curl -f -s http://***********:3210/api/health > /dev/null; then
    echo "LobeChat is healthy"
    exit 0
else
    echo "LobeChat is unhealthy"
    exit 1
fi
EOF

chmod +x /opt/scripts/check-lobechat.sh

# 添加到 crontab
echo "*/5 * * * * /opt/scripts/check-lobechat.sh" | crontab -
```

### 2. 备份策略

```bash
# 备份 LobeChat 数据
docker exec lobechat tar czf /tmp/lobechat-backup-$(date +%Y%m%d).tar.gz /app/data

# 定期清理日志
find /var/log/nginx -name "*.log" -mtime +30 -delete
```

### 3. 更新流程

```bash
# 1. 备份数据
docker exec lobechat cp -r /app/data /app/data.backup

# 2. 更新镜像
docker-compose pull lobechat

# 3. 重启服务
docker-compose up -d lobechat

# 4. 验证功能
node scripts/test-lobechat-integration.js
```

## 总结

完成以上步骤后，LobeChat 将成功集成到 YNNX AI 平台中，用户可以通过平台的统一认证系统无缝访问智能对话功能，同时保证用户会话的隔离和数据安全。

如遇到问题，请参考故障排除部分或查看相关日志文件。
