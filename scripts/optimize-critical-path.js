#!/usr/bin/env node

// YNNX AI Platform - 关键路径优化脚本
// 在构建完成后运行，优化关键渲染路径

/* eslint-env node */
/* global process */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 开始关键路径优化...');

try {
  const distPath = path.join(__dirname, '../dist');
  
  // 检查 dist 目录是否存在
  if (!fs.existsSync(distPath)) {
    console.log('⚠️  dist 目录不存在，跳过优化');
    process.exit(0);
  }
  
  // 获取构建统计信息
  const files = fs.readdirSync(distPath);
  const jsFiles = files.filter(file => file.endsWith('.js'));
  const cssFiles = files.filter(file => file.endsWith('.css'));
  
  console.log(`📊 构建统计:`);
  console.log(`   JavaScript 文件: ${jsFiles.length}`);
  console.log(`   CSS 文件: ${cssFiles.length}`);
  console.log(`   总文件数: ${files.length}`);
  
  // 这里可以添加更多优化逻辑，例如：
  // - 压缩 HTML
  // - 内联关键 CSS
  // - 预加载重要资源
  // - 生成 Service Worker 预缓存列表
  
  console.log('✅ 关键路径优化完成');
  
} catch (error) {
  console.warn('⚠️  关键路径优化过程中出现警告:', error.message);
  // 不让构建失败，仅输出警告
  process.exit(0);
} 