/**
 * 文档加载测试工具
 * 用于测试文档库加载和搜索功能
 */

import enhancedDocumentService from '../services/documentService';

class DocumentLoadingTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('[文档测试] 开始运行所有测试...');
    
    const results = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {
        passed: 0,
        failed: 0,
        total: 0
      }
    };

    // 测试1: 基本加载功能
    await this.testBasicLoading(results);
    
    // 测试2: 搜索功能
    await this.testSearchFunctionality(results);
    
    // 测试3: 状态检查
    await this.testStatusChecking(results);
    
    // 测试4: 错误处理
    await this.testErrorHandling(results);

    // 计算总结
    results.summary.total = results.tests.length;
    results.summary.passed = results.tests.filter(t => t.status === 'passed').length;
    results.summary.failed = results.tests.filter(t => t.status === 'failed').length;
    
    console.log('[文档测试] 测试完成:', results);
    return results;
  }

  /**
   * 测试基本加载功能
   */
  async testBasicLoading(results) {
    const test = {
      name: '基本加载功能',
      status: 'running',
      details: [],
      startTime: Date.now()
    };

    try {
      // 清理缓存
      enhancedDocumentService.clearCache();
      test.details.push('已清理缓存');

      // 预加载文档
      await enhancedDocumentService.preloadAllDocuments();
      test.details.push('预加载完成');

      // 检查加载状态
      const stats = await enhancedDocumentService.getDocumentStats();
      test.details.push(`加载了 ${stats.totalDocuments} 个文档`);
      test.details.push(`总计 ${stats.totalWords} 个词`);

      if (stats.canSearch) {
        test.status = 'passed';
        test.details.push('✅ 文档库可用于搜索');
      } else {
        test.status = 'failed';
        test.details.push('❌ 文档库不可用于搜索');
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`❌ 加载失败: ${error.message}`);
    }

    test.endTime = Date.now();
    test.duration = test.endTime - test.startTime;
    results.tests.push(test);
  }

  /**
   * 测试搜索功能
   */
  async testSearchFunctionality(results) {
    const test = {
      name: '搜索功能',
      status: 'running',
      details: [],
      startTime: Date.now()
    };

    try {
      // 测试不同的搜索查询
      const queries = ['安装', 'installation', '配置', 'config', '使用'];
      let successCount = 0;

      for (const query of queries) {
        try {
          const searchResults = await enhancedDocumentService.searchDocuments(query, { maxResults: 3 });
          if (searchResults.length > 0) {
            successCount++;
            test.details.push(`✅ "${query}": 找到 ${searchResults.length} 个结果`);
          } else {
            test.details.push(`⚠️ "${query}": 未找到结果`);
          }
        } catch (error) {
          test.details.push(`❌ "${query}": 搜索失败 - ${error.message}`);
        }
      }

      if (successCount >= queries.length / 2) {
        test.status = 'passed';
        test.details.push(`✅ 搜索功能正常 (${successCount}/${queries.length} 成功)`);
      } else {
        test.status = 'failed';
        test.details.push(`❌ 搜索功能异常 (${successCount}/${queries.length} 成功)`);
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`❌ 搜索测试失败: ${error.message}`);
    }

    test.endTime = Date.now();
    test.duration = test.endTime - test.startTime;
    results.tests.push(test);
  }

  /**
   * 测试状态检查
   */
  async testStatusChecking(results) {
    const test = {
      name: '状态检查',
      status: 'running',
      details: [],
      startTime: Date.now()
    };

    try {
      // 检查各种状态方法
      const stats = await enhancedDocumentService.getDocumentStats();
      const loadingStatus = enhancedDocumentService.getLoadingStatus();
      const isReady = enhancedDocumentService.isReadyForSearch();

      test.details.push(`文档统计: ${JSON.stringify({
        totalDocuments: stats.totalDocuments,
        isLoaded: stats.isLoaded,
        canSearch: stats.canSearch
      })}`);

      test.details.push(`加载状态: ${loadingStatus.status} - ${loadingStatus.message}`);
      test.details.push(`搜索就绪: ${isReady}`);

      if (stats.canSearch && isReady && loadingStatus.status === 'loaded') {
        test.status = 'passed';
        test.details.push('✅ 所有状态检查正常');
      } else {
        test.status = 'failed';
        test.details.push('❌ 状态检查异常');
      }
    } catch (error) {
      test.status = 'failed';
      test.details.push(`❌ 状态检查失败: ${error.message}`);
    }

    test.endTime = Date.now();
    test.duration = test.endTime - test.startTime;
    results.tests.push(test);
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling(results) {
    const test = {
      name: '错误处理',
      status: 'running',
      details: [],
      startTime: Date.now()
    };

    try {
      // 测试在未加载状态下搜索
      enhancedDocumentService.allDocumentsLoaded = false;
      enhancedDocumentService.loading = false;

      try {
        const searchResults = await enhancedDocumentService.searchDocuments('test');
        if (searchResults.length >= 0) {
          test.details.push('✅ 未加载状态下搜索处理正常');
        }
      } catch (error) {
        if (error.message.includes('加载')) {
          test.details.push('✅ 正确抛出加载错误');
        } else {
          test.details.push(`⚠️ 错误信息不符合预期: ${error.message}`);
        }
      }

      // 恢复正常状态
      await enhancedDocumentService.preloadAllDocuments();
      test.details.push('✅ 状态恢复正常');

      test.status = 'passed';
    } catch (error) {
      test.status = 'failed';
      test.details.push(`❌ 错误处理测试失败: ${error.message}`);
    }

    test.endTime = Date.now();
    test.duration = test.endTime - test.startTime;
    results.tests.push(test);
  }

  /**
   * 模拟用户搜索场景
   */
  async simulateUserSearchScenario() {
    console.log('[文档测试] 模拟用户搜索场景...');
    
    const scenarios = [
      {
        name: '新用户安装指南',
        query: '如何安装',
        expectedKeywords: ['安装', 'install', '下载']
      },
      {
        name: '配置问题',
        query: '配置文件在哪里',
        expectedKeywords: ['配置', 'config', '设置']
      },
      {
        name: '使用教程',
        query: '怎么使用',
        expectedKeywords: ['使用', '教程', '指南']
      },
      {
        name: '常见问题',
        query: '常见问题',
        expectedKeywords: ['问题', 'FAQ', '解决']
      }
    ];

    const results = [];

    for (const scenario of scenarios) {
      const result = {
        scenario: scenario.name,
        query: scenario.query,
        success: false,
        details: []
      };

      try {
        const searchResults = await enhancedDocumentService.searchDocuments(scenario.query);
        
        if (searchResults.length > 0) {
          result.success = true;
          result.details.push(`找到 ${searchResults.length} 个相关结果`);
          
          // 检查结果相关性
          const hasRelevantContent = searchResults.some(r => 
            scenario.expectedKeywords.some(keyword => 
              r.document?.title?.toLowerCase().includes(keyword.toLowerCase()) ||
              r.matches?.some(m => m.content?.toLowerCase().includes(keyword.toLowerCase()))
            )
          );
          
          if (hasRelevantContent) {
            result.details.push('✅ 结果内容相关');
          } else {
            result.details.push('⚠️ 结果相关性待提升');
          }
        } else {
          result.details.push('❌ 未找到相关结果');
        }
      } catch (error) {
        result.details.push(`❌ 搜索失败: ${error.message}`);
      }

      results.push(result);
    }

    console.log('[文档测试] 用户场景测试完成:', results);
    return results;
  }
}

// 创建全局实例
const documentLoadingTest = new DocumentLoadingTest();

export default documentLoadingTest;
