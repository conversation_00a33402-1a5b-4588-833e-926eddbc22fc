# 文档系统使用指南

这个目录包含了AI开发平台的所有技术文档，采用Markdown格式编写，支持图片、视频等媒体内容。

## 目录结构

```
public/docs/
├── config.json          # 文档配置文件
├── README.md            # 本说明文件
├── cline/               # Cline插件相关文档
│   ├── install-guide.md
│   ├── advanced-features.md
│   ├── images/          # 图片资源
│   └── videos/          # 视频资源
├── roocode/             # RooCode插件相关文档
│   ├── install-guide.md
│   ├── multi-mode.md
│   ├── images/
│   └── videos/
├── jetbrains/           # JetBrains AI Assistant相关文档
│   ├── install-guide.md
│   ├── features.md
│   ├── images/
│   └── videos/
└── faq/                 # 常见问题文档
    ├── common-faq.md
    ├── images/
    └── videos/
```

## 文档配置

文档的元数据在 `config.json` 中定义，包括：
- 分类信息（categories）
- 文档列表（documents）
- 每个文档的标题、描述、标签等信息

## 添加新文档

1. **创建Markdown文件**：在相应的分类目录下创建 `.md` 文件
2. **更新配置**：在 `config.json` 中添加文档元数据
3. **添加媒体文件**：将图片放在 `images/` 目录，视频放在 `videos/` 目录

### 示例配置

```json
{
  "documents": {
    "新分类": [
      {
        "id": "unique-doc-id",
        "title": "文档标题",
        "description": "文档描述",
        "readTime": "5 分钟",
        "tags": ["标签1", "标签2"],
        "file": "category/document.md"
      }
    ]
  }
}
```

## Markdown语法支持

支持的Markdown功能：
- ✅ 标题、段落、列表
- ✅ 代码块和代码高亮
- ✅ 表格
- ✅ 图片和视频
- ✅ HTML标签
- ✅ GitHub风格Markdown (GFM)

### 图片引用

```markdown
![图片描述](./images/screenshot.png)
```

### 视频引用

```markdown
<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>
```

### 代码高亮

支持多种编程语言的语法高亮：

```javascript
const example = {
  language: "javascript",
  highlight: true
};
```

## 最佳实践

1. **文件命名**：使用小写字母和连字符，如 `install-guide.md`
2. **图片格式**：推荐使用 PNG 或 WEBP 格式
3. **视频格式**：推荐使用 MP4 格式
4. **文件大小**：图片建议小于 500KB，视频建议小于 10MB
5. **相对路径**：使用相对路径引用媒体文件

## 缓存机制

文档系统具有智能缓存功能：
- 文档内容会缓存在浏览器中
- 配置文件会自动更新
- 媒体文件会优化加载

## 故障排除

### 文档加载失败
- 检查文件路径是否正确
- 确认 `config.json` 配置是否正确
- 查看浏览器控制台错误信息

### 图片显示异常
- 确认图片文件存在
- 检查图片格式是否支持
- 验证相对路径是否正确

### 搜索功能异常
- 重新加载页面清除缓存
- 检查文档内容是否包含搜索关键词

---

*最后更新时间：2024年12月* 