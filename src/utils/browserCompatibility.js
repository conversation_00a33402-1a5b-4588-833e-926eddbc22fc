/**
 * 浏览器兼容性检测工具
 */

// 浏览器最低版本要求 - 基于项目实际使用的特性
export const MINIMUM_BROWSER_VERSIONS = {
  Chrome: 60,    // 支持 ES2020, async/await, modern CSS Grid
  Firefox: 60,   // 支持 ES2020, 现代 JS 特性
  Safari: 12,    // 支持 CSS Grid, ES2020, Service Worker
  Edge: 79,      // 基于 Chromium 的现代 Edge
  Opera: 57,     // 基于 Chromium
  Samsung: 10    // 移动端 Samsung Internet
};

/**
 * 检测浏览器信息
 * @returns {Object} 浏览器信息对象
 */
export const getBrowserInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const browser = {
    name: 'Unknown',
    version: 0,
    fullVersion: 'Unknown',
    engine: 'Unknown',
    platform: 'Unknown'
  };

  // 检测浏览器平台
  if (userAgent.indexOf('windows') > -1) browser.platform = 'Windows';
  else if (userAgent.indexOf('macintosh') > -1) browser.platform = 'macOS';
  else if (userAgent.indexOf('linux') > -1) browser.platform = 'Linux';
  else if (userAgent.indexOf('android') > -1) browser.platform = 'Android';
  else if (userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1) browser.platform = 'iOS';

  // 检测浏览器类型和版本
  let match;

  // Chrome (必须在 Edge 之前检测)
  if ((match = userAgent.match(/chrome\/(\d+)\.(\d+)\.(\d+)/)) && userAgent.indexOf('edge') === -1 && userAgent.indexOf('opr') === -1) {
    browser.name = 'Chrome';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}.${match[3]}`;
    browser.engine = 'Blink';
  }
  // Firefox
  else if ((match = userAgent.match(/firefox\/(\d+)\.(\d+)/))) {
    browser.name = 'Firefox';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Gecko';
  }
  // Safari (必须在 Chrome 之前检测，因为 Chrome 也包含 safari 字符串)
  else if ((match = userAgent.match(/version\/(\d+)\.(\d+).*safari/)) && userAgent.indexOf('chrome') === -1) {
    browser.name = 'Safari';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'WebKit';
  }
  // Edge (新版基于 Chromium)
  else if ((match = userAgent.match(/edg\/(\d+)\.(\d+)\.(\d+)/))) {
    browser.name = 'Edge';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}.${match[3]}`;
    browser.engine = 'Blink';
  }
  // Edge (旧版)
  else if ((match = userAgent.match(/edge\/(\d+)\.(\d+)/))) {
    browser.name = 'Edge';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'EdgeHTML';
  }
  // Opera
  else if ((match = userAgent.match(/opr\/(\d+)\.(\d+)/))) {
    browser.name = 'Opera';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Blink';
  }
  // Samsung Internet
  else if ((match = userAgent.match(/samsungbrowser\/(\d+)\.(\d+)/))) {
    browser.name = 'Samsung';
    browser.version = parseInt(match[1]);
    browser.fullVersion = `${match[1]}.${match[2]}`;
    browser.engine = 'Blink';
  }
  // Internet Explorer
  else if ((match = userAgent.match(/(?:msie |trident.*rv:)(\d+)/))) {
    browser.name = 'Internet Explorer';
    browser.version = parseInt(match[1]);
    browser.fullVersion = match[1];
    browser.engine = 'Trident';
  }

  return browser;
};

/**
 * 检测浏览器特性支持
 * @returns {Object} 特性支持检测结果
 */
export const checkFeatureSupport = () => {
  const features = {};

  // JavaScript 特性检测
  features.es6Support = (() => {
    try {
      // 检测箭头函数、模板字符串、解构赋值等特性
      // 使用更安全的方式检测而不是 eval
      return (
        typeof Symbol !== 'undefined' &&
        typeof Promise !== 'undefined' &&
        typeof Map !== 'undefined' &&
        typeof Set !== 'undefined' &&
        Array.prototype.includes &&
        Object.assign &&
        // 检测箭头函数支持
        (() => {
          try {
            return (function() { return () => {}; })()() === undefined;
          } catch(e) {
            return false;
          }
        })()
      );
    } catch (e) {
      return false;
    }
  })();

  features.es2017Support = (() => {
    try {
      // 检测 async/await 支持
      return typeof (async () => {})().then === 'function';
    } catch (e) {
      return false;
    }
  })();

  // API 特性检测
  features.fetchAPI = typeof fetch !== 'undefined';
  features.promiseSupport = typeof Promise !== 'undefined' && typeof Promise.prototype.finally === 'function';
  features.arrayMethods = Array.prototype.includes && Array.prototype.find && Array.prototype.findIndex;
  features.objectMethods = Object.assign && Object.keys && Object.values;
  features.mapSet = typeof Map !== 'undefined' && typeof Set !== 'undefined';
  features.symbols = typeof Symbol !== 'undefined';
  features.weakMapSet = typeof WeakMap !== 'undefined' && typeof WeakSet !== 'undefined';

  // Web API 特性检测
  features.webSocket = typeof WebSocket !== 'undefined';
  features.webWorker = typeof Worker !== 'undefined';
  features.serviceWorker = 'serviceWorker' in navigator;
  features.pushNotifications = 'PushManager' in window;
  features.geolocation = 'geolocation' in navigator;
  features.deviceOrientation = 'DeviceOrientationEvent' in window;
  features.vibration = 'vibrate' in navigator;

  // Storage 特性检测
  features.localStorage = (() => {
    try {
      return typeof localStorage !== 'undefined' && localStorage !== null;
    } catch (e) {
      return false;
    }
  })();

  features.sessionStorage = (() => {
    try {
      return typeof sessionStorage !== 'undefined' && sessionStorage !== null;
    } catch (e) {
      return false;
    }
  })();

  features.indexedDB = 'indexedDB' in window;

  // CSS 特性检测
  const testElement = document.createElement('div');
  features.cssGrid = 'grid' in testElement.style;
  features.cssFlexbox = 'flex' in testElement.style || 'webkitFlex' in testElement.style;
  features.cssCustomProperties = CSS && CSS.supports && CSS.supports('--custom-property', 'value');
  features.cssTransforms = 'transform' in testElement.style || 'webkitTransform' in testElement.style;
  features.cssTransitions = 'transition' in testElement.style || 'webkitTransition' in testElement.style;
  features.cssAnimations = 'animation' in testElement.style || 'webkitAnimation' in testElement.style;

  // 媒体特性检测
  features.webGL = (() => {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch (e) {
      return false;
    }
  })();

  features.canvas = (() => {
    try {
      return !!document.createElement('canvas').getContext('2d');
    } catch (e) {
      return false;
    }
  })();

  features.svg = document.implementation.hasFeature('http://www.w3.org/TR/SVG11/feature#BasicStructure', '1.1');
  features.audio = !!document.createElement('audio').canPlayType;
  features.video = !!document.createElement('video').canPlayType;

  // 输入特性检测
  features.touch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  features.pointerEvents = 'onpointerdown' in window;

  return features;
};

/**
 * 综合兼容性检测
 * @returns {Object} 兼容性检测结果
 */
export const checkBrowserCompatibility = () => {
  const browser = getBrowserInfo();
  const features = checkFeatureSupport();
  
  // 检查浏览器版本是否满足最低要求
  const isVersionSupported = MINIMUM_BROWSER_VERSIONS[browser.name] 
    ? browser.version >= MINIMUM_BROWSER_VERSIONS[browser.name]
    : browser.name !== 'Internet Explorer'; // IE 完全不支持

  // 检查关键特性是否支持
  const criticalFeatures = [
    'es6Support',
    'fetchAPI',
    'promiseSupport',
    'localStorage',
    'cssFlexbox'
  ];

  const unsupportedFeatures = criticalFeatures.filter(feature => !features[feature]);
  const isFeaturesSupported = unsupportedFeatures.length === 0;

  // 检查推荐特性是否支持
  const recommendedFeatures = [
    'es2017Support',
    'serviceWorker',
    'cssGrid',
    'webSocket'
  ];

  const missingRecommendedFeatures = recommendedFeatures.filter(feature => !features[feature]);

  const isCompatible = isVersionSupported && isFeaturesSupported;

  return {
    isCompatible,
    browser,
    features,
    issues: {
      versionTooOld: !isVersionSupported,
      unsupportedFeatures,
      missingRecommendedFeatures
    },
    recommendations: getCompatibilityRecommendations(browser, isCompatible, unsupportedFeatures)
  };
};

/**
 * 获取兼容性建议
 * @param {Object} browser 浏览器信息
 * @param {boolean} isCompatible 是否兼容
 * @param {Array} unsupportedFeatures 不支持的特性列表
 * @returns {Array} 建议列表
 */
export const getCompatibilityRecommendations = (browser, isCompatible, unsupportedFeatures = []) => {
  const recommendations = [];

  if (!isCompatible) {
    if (browser.name === 'Internet Explorer') {
      recommendations.push({
        type: 'critical',
        message: 'Internet Explorer 已不再受支持，请升级到现代浏览器以获得最佳体验',
        action: 'upgrade'
      });
    } else if (MINIMUM_BROWSER_VERSIONS[browser.name] && browser.version < MINIMUM_BROWSER_VERSIONS[browser.name]) {
      recommendations.push({
        type: 'warning',
        message: `您的 ${browser.name} 版本 (${browser.version}) 过旧，建议升级到 ${MINIMUM_BROWSER_VERSIONS[browser.name]} 或更高版本`,
        action: 'update'
      });
    }

    if (unsupportedFeatures.includes('es6Support')) {
      recommendations.push({
        type: 'critical',
        message: '浏览器不支持现代 JavaScript 语法，网站功能可能无法正常工作',
        action: 'upgrade'
      });
    }

    if (unsupportedFeatures.includes('fetchAPI')) {
      recommendations.push({
        type: 'warning',
        message: '浏览器不支持 Fetch API，网络请求可能受到影响',
        action: 'polyfill'
      });
    }

    if (unsupportedFeatures.includes('localStorage')) {
      recommendations.push({
        type: 'warning',
        message: '浏览器不支持本地存储，某些功能可能无法保存设置',
        action: 'fallback'
      });
    }
  }

  return recommendations;
};

/**
 * 获取推荐浏览器列表
 * @returns {Array} 推荐浏览器列表
 */
export const getRecommendedBrowsers = () => [
  {
    name: 'Google Chrome',
    minVersion: MINIMUM_BROWSER_VERSIONS.Chrome,
    downloadUrl: null, // 内网环境：请联系IT部门获取浏览器安装包
    icon: '🌐',
    description: '快速、安全、功能丰富'
  },
  {
    name: 'Mozilla Firefox',
    minVersion: MINIMUM_BROWSER_VERSIONS.Firefox,
    downloadUrl: null, // 内网环境：请联系IT部门获取浏览器安装包
    icon: '🦊',
    description: '注重隐私、开源、可定制'
  },
  {
    name: 'Microsoft Edge',
    minVersion: MINIMUM_BROWSER_VERSIONS.Edge,
    downloadUrl: null, // 内网环境：请联系IT部门获取浏览器安装包
    icon: '🔷',
    description: 'Windows 系统推荐浏览器'
  },
  {
    name: 'Safari',
    minVersion: MINIMUM_BROWSER_VERSIONS.Safari,
    downloadUrl: null, // 内网环境：请联系IT部门获取浏览器安装包
    icon: '🧭',
    description: 'macOS 和 iOS 设备专用'
  }
];

/**
 * 记录兼容性检测结果（用于统计分析）
 * @param {Object} compatibilityResult 兼容性检测结果
 */
export const logCompatibilityData = (compatibilityResult) => {
  try {
    const logData = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      browser: compatibilityResult.browser,
      isCompatible: compatibilityResult.isCompatible,
      issues: compatibilityResult.issues,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    };

    // 可以发送到分析服务器或保存到本地存储
    console.log('Browser Compatibility Check:', logData);
    
    // 如果支持，保存到本地存储用于调试
    if (compatibilityResult.features.localStorage) {
      localStorage.setItem('browserCompatibilityLog', JSON.stringify(logData));
    }
  } catch (error) {
    console.warn('Failed to log compatibility data:', error);
  }
};