#!/usr/bin/env node

/**
 * 内网部署准备脚本
 * 自动处理外部链接依赖，生成适合内网环境的版本
 */

const fs = require('fs');
const path = require('path');

console.log('🌐 准备内网部署版本...\n');

// 1. 修改浏览器兼容性组件
console.log('📝 处理浏览器兼容性组件...');
const browserCompatibilityPath = 'src/utils/browserCompatibility.js';

if (fs.existsSync(browserCompatibilityPath)) {
  let browserCompatibilityContent = fs.readFileSync(browserCompatibilityPath, 'utf-8');

  // 移除外部下载链接
  browserCompatibilityContent = browserCompatibilityContent.replace(
    /downloadUrl: 'https:\/\/[^']+'/g,
    'downloadUrl: null // 内网环境：请联系IT部门获取浏览器安装包'
  );

  // 备份原文件
  fs.writeFileSync(browserCompatibilityPath + '.backup', fs.readFileSync(browserCompatibilityPath, 'utf-8'));
  fs.writeFileSync(browserCompatibilityPath, browserCompatibilityContent);
  console.log('✅ 已移除浏览器兼容性组件中的外部链接');
} else {
  console.log('⚠️  浏览器兼容性组件文件不存在，跳过');
}

// 2. 修改index.html
console.log('\n📝 处理HTML中的外部链接...');
const indexPath = 'index.html';

if (fs.existsSync(indexPath)) {
  let indexContent = fs.readFileSync(indexPath, 'utf-8');

  // 备份原文件
  fs.writeFileSync(indexPath + '.backup', indexContent);

  // 替换浏览器下载链接
  indexContent = indexContent.replace(
    /href="https:\/\/[^"]+" target="_blank"/g,
    'onclick="alert(\'请联系IT部门获取浏览器安装包\')" style="cursor: pointer; text-decoration: none;"'
  );

  fs.writeFileSync(indexPath, indexContent);
  console.log('✅ 已修改HTML中的外部链接');
} else {
  console.log('⚠️  index.html文件不存在，跳过');
}

// 3. 处理服务器配置文件中的默认URL
console.log('\n📝 处理服务器配置文件...');
const serverFiles = [

  'src/server/ldapAuthServer.js'
];

serverFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // 备份原文件
    fs.writeFileSync(filePath + '.backup', content);
    
    // 添加注释说明这些是默认值
    content = content.replace(
      /return process\.env\.PROD_FRONTEND_URL \|\| 'https:\/\/[^']+'/g,
      "return process.env.PROD_FRONTEND_URL || 'http://localhost:5173' // 内网环境：请通过环境变量配置实际地址"
    );
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ 已处理 ${filePath}`);
  }
});

// 4. 创建内网环境配置
console.log('\n📝 创建内网环境配置文件...');
const intranetEnvContent = `# 内网环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# 运行环境
NODE_ENV=production

# 前端访问地址（替换为实际的内网地址）
PROD_FRONTEND_URL=http://your-intranet-domain.com

# 内网部署标识
DISABLE_EXTERNAL_LINKS=true

# 如果有内网浏览器下载服务器，配置此地址
BROWSER_DOWNLOAD_BASE_URL=

# 服务配置（根据实际情况修改）
LDAP_SERVER=your-ldap-server
LDAP_PORT=389
LDAP_BIND_DN=cn=admin,dc=company,dc=com
LDAP_SEARCH_BASE=ou=users,dc=company,dc=com

# API配置（已不再需要单独的NLWEB API）

# LiteLLM API 配置  
LITELLM_API_URL=http://your-litellm-api:port
LITELLM_API_KEY=your-api-key

# 其他内网服务配置
# 根据实际部署的服务进行配置
`;

fs.writeFileSync('.env.intranet', intranetEnvContent);
console.log('✅ 已创建内网环境配置文件: .env.intranet');

// 5. 创建恢复脚本
console.log('\n📝 创建恢复脚本...');
const restoreScript = `#!/usr/bin/env node

/**
 * 恢复原始文件脚本
 * 用于撤销内网部署修改
 */

const fs = require('fs');

console.log('🔄 恢复原始文件...');

const backupFiles = [
  'src/utils/browserCompatibility.js.backup',
  'index.html.backup',
   
  'src/server/ldapAuthServer.js.backup'
];

backupFiles.forEach(backupFile => {
  if (fs.existsSync(backupFile)) {
    const originalFile = backupFile.replace('.backup', '');
    fs.copyFileSync(backupFile, originalFile);
    fs.unlinkSync(backupFile);
    console.log(\`✅ 已恢复 \${originalFile}\`);
  }
});

// 删除内网配置文件
if (fs.existsSync('.env.intranet')) {
  fs.unlinkSync('.env.intranet');
  console.log('✅ 已删除内网配置文件');
}

console.log('\\n🎉 恢复完成！');
`;

fs.writeFileSync('scripts/restore-original-files.cjs', restoreScript);
fs.chmodSync('scripts/restore-original-files.cjs', '755');
console.log('✅ 已创建恢复脚本: scripts/restore-original-files.cjs');

// 6. 显示总结信息
console.log('\n🎉 内网部署准备完成！\n');

console.log('📊 修改总结:');
console.log('✅ 浏览器兼容性组件: 移除外部下载链接');
console.log('✅ HTML页面: 替换外部链接为提示信息');
console.log('✅ 服务器配置: 修改默认URL为本地地址');
console.log('✅ 环境配置: 创建内网配置模板');
console.log('✅ 恢复脚本: 可一键恢复原始文件\n');

console.log('📋 下一步操作:');
console.log('1. 编辑 .env.intranet 配置文件，填入实际的内网地址');
console.log('2. 运行: cp .env.intranet .env');
console.log('3. 构建项目: npm run build');
console.log('4. 验证无外部依赖: npm run check:external');
console.log('5. 部署到内网服务器\n');

console.log('🔄 如需恢复原始文件:');
console.log('   node scripts/restore-original-files.cjs\n');

console.log('🎯 内网部署优势:');
console.log('• 所有静态资源已本地化');
console.log('• 字体文件无CDN依赖');
console.log('• JavaScript库通过npm管理');
console.log('• 可离线正常运行（除API调用外）');

console.log('\n✅ 您的项目现在已准备好在内网环境中部署！'); 