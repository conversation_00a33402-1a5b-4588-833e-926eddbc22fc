# 工具下载目录结构说明

本文档描述了平台工具下载的完整目录结构和使用方法。

## 📁 目录结构

```
public/downloads/tools/
├── vscode-ide/                    # VS Code IDE
│   ├── windows/                   # Windows 版本
│   ├── macos/                     # macOS 版本
│   ├── linux/                     # Linux 版本
│   └── README.md                  # 总体说明
├── cline-plugin/                  # Cline 插件
│   ├── latest/                    # 最新版本
│   ├── stable/                    # 稳定版本
│   ├── archive/                   # 历史版本
│   └── README.md                  # 总体说明
├── roocode-plugin/                # RooCode 插件
│   ├── latest/                    # 最新版本
│   ├── stable/                    # 稳定版本
│   ├── beta/                      # 测试版本
│   └── README.md                  # 总体说明
├── jetbrains-ai-assistant/        # JetBrains AI 助手
│   ├── latest/                    # 最新通用版本
│   ├── stable/                    # 稳定版本
│   ├── ides/                      # IDE 特定版本
│   │   ├── idea/                  # IntelliJ IDEA 版本
│   │   ├── pycharm/               # PyCharm 版本
│   │   └── webstorm/              # WebStorm 版本
│   └── README.md                  # 总体说明
└── DIRECTORY_STRUCTURE.md         # 本说明文件
```

## 🔧 管理员使用指南

### 1. 文件放置规则
- 将最新的安装文件放置在对应的目录中，使用预设的文件名
- 直接替换现有文件即可更新版本
- 平台会直接链接到这些预设文件名

### 2. 预设文件名格式
- VS Code Windows: `VSCodeUserSetup-x64-latest.exe`
- VS Code macOS: `VSCode-darwin-universal-latest.zip`
- VS Code Linux: `code_latest_amd64.deb`
- Cline 最新版: `cline-latest.vsix`
- Cline 稳定版: `cline-stable.vsix`
- RooCode 最新版: `roocode-latest.vsix`
- RooCode 稳定版: `roocode-stable.vsix`
- RooCode 测试版: `roocode-beta.vsix`
- JetBrains 通用版: `jetbrains-ai-assistant-latest.jar`
- JetBrains IDEA版: `idea-ai-assistant-latest.jar`
- JetBrains PyCharm版: `pycharm-ai-assistant-latest.jar`
- JetBrains WebStorm版: `webstorm-ai-assistant-latest.jar`

### 3. 版本管理
- **latest/**: 最新开发版本，功能最新但可能不稳定
- **stable/**: 经过测试的稳定版本，推荐生产使用
- **beta/**: 测试版本，供体验新功能使用
- **archive/**: 历史版本存档，供特殊需求使用

## 👥 用户使用说明

### 1. 下载流程
1. 在平台点击"下载"按钮
2. 选择合适的版本/平台
3. 点击"下载"按钮直接下载文件
4. 文件会自动下载到本地

### 2. 版本选择建议
- **首次安装**: 建议选择 `stable/` 目录中的稳定版本
- **追求新功能**: 可选择 `latest/` 目录中的最新版本
- **测试体验**: 可选择 `beta/` 目录中的测试版本

### 3. 直接下载特性
- 点击下载按钮即可直接获取文件
- 无需手动选择，系统自动下载预设文件
- 管理员确保文件始终为最新版本

## ⚠️ 注意事项

1. **文件完整性**: 下载完成后请检查文件大小和完整性
2. **版本兼容性**: 确保下载的版本与您的系统/IDE兼容
3. **安全扫描**: 建议对下载的文件进行安全扫描
4. **备份设置**: 更新插件前请备份重要的配置设置

## 🔄 更新机制

- 管理员会定期更新各目录中的文件
- 新版本文件会保留旧版本文件一段时间
- 过期版本会移至 `archive/` 目录或删除
- 重要更新会在平台上发布通知

---
*最后更新: 2025-01 | 如有问题请联系技术支持团队* 