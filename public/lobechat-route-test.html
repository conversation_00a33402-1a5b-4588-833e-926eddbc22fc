<!DOCTYPE html>
<html>
<head>
    <title>LobeChat 路由测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin: 10px 0; }
        #log { background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>LobeChat 路由测试</h1>
    
    <div class="test-section info">
        <h3>路由导航测试</h3>
        <p>点击下面的按钮测试不同路由的导航：</p>
        <button onclick="testRoute('/chat/chat')">聊天页面</button>
        <button onclick="testRoute('/chat/files')">文件页面</button>
        <button onclick="testRoute('/chat/image')">图像页面</button>
        <button onclick="testRoute('/chat/discover')">发现页面</button>
        <button onclick="testRoute('/chat/')">根路径</button>
    </div>
    
    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h3>当前测试的路由</h3>
        <div id="current-route">点击上面的按钮开始测试</div>
        <iframe id="test-iframe" src="about:blank"></iframe>
    </div>

    <script>
        const log = document.getElementById('log');
        const currentRoute = document.getElementById('current-route');
        const iframe = document.getElementById('test-iframe');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        function testRoute(route) {
            addLog(`开始测试路由: ${route}`, 'info');
            currentRoute.textContent = `正在测试: ${route}`;
            
            // 设置 iframe 源
            iframe.src = route;
            
            // 监听 iframe 加载
            iframe.onload = function() {
                addLog(`路由 ${route} 加载完成`, 'success');
                
                // 尝试检查 iframe 内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        const title = iframeDoc.title;
                        addLog(`页面标题: ${title}`, 'info');
                        
                        // 检查是否有错误
                        const errorElements = iframeDoc.querySelectorAll('.error, .next-error-h1');
                        if (errorElements.length > 0) {
                            addLog(`发现 ${errorElements.length} 个错误元素`, 'error');
                        } else {
                            addLog('页面加载正常，未发现错误', 'success');
                        }
                    }
                } catch (e) {
                    addLog(`无法检查 iframe 内容: ${e.message}`, 'info');
                }
            };
            
            iframe.onerror = function() {
                addLog(`路由 ${route} 加载失败`, 'error');
            };
        }
        
        // 页面加载完成后的初始化
        addLog('路由测试页面已加载', 'success');
        addLog('请点击上面的按钮测试不同的路由', 'info');
    </script>
</body>
</html>
