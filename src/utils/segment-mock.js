// 空的segment analytics模块，用于浏览器环境
// 这个模块防止@segment/analytics-node在浏览器环境中导致错误

// 创建空的Analytics类
class Analytics {
  constructor() {
    // 空构造函数
  }

  track() {
    // 空的track方法
    return Promise.resolve();
  }

  identify() {
    // 空的identify方法
    return Promise.resolve();
  }

  page() {
    // 空的page方法
    return Promise.resolve();
  }

  group() {
    // 空的group方法
    return Promise.resolve();
  }

  alias() {
    // 空的alias方法
    return Promise.resolve();
  }

  flush() {
    // 空的flush方法
    return Promise.resolve();
  }

  close() {
    // 空的close方法
    return Promise.resolve();
  }
}

// 导出默认实例和类
export { Analytics };
export default Analytics;

// 兼容性导出 - 仅使用ES模块语法
// 为了确保在不同导入方式下都能工作 