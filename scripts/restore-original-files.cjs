#!/usr/bin/env node

/**
 * 恢复原始文件脚本
 * 用于撤销内网部署修改
 */

const fs = require('fs');

console.log('🔄 恢复原始文件...');

const backupFiles = [
  'src/utils/browserCompatibility.js.backup',
  'index.html.backup',
   
  'src/server/ldapAuthServer.js.backup'
];

backupFiles.forEach(backupFile => {
  if (fs.existsSync(backupFile)) {
    const originalFile = backupFile.replace('.backup', '');
    fs.copyFileSync(backupFile, originalFile);
    fs.unlinkSync(backupFile);
    console.log(`✅ 已恢复 ${originalFile}`);
  }
});

// 删除内网配置文件
if (fs.existsSync('.env.intranet')) {
  fs.unlinkSync('.env.intranet');
  console.log('✅ 已删除内网配置文件');
}

console.log('\n🎉 恢复完成！');
