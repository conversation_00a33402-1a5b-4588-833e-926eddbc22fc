# RooCode 插件下载目录

本目录包含 RooCode 多模式 AI 助手插件的安装文件。

## 目录结构
```
roocode-plugin/
├── latest/          # 最新版本插件文件
├── stable/          # 稳定版本插件文件
├── beta/            # 测试版本插件文件
└── README.md        # 本说明文件
```

## 使用说明
1. 建议下载 `latest/` 目录中的最新版本
2. 如需稳定性，可选择 `stable/` 目录中的稳定版本
3. 体验新功能可选择 `beta/` 目录中的测试版本
4. 下载 .vsix 文件后，在 VS Code 中手动安装

## 文件命名规范
- 最新版: `roocode-[version].vsix`
- 稳定版: `roocode-stable-[version].vsix`
- 测试版: `roocode-beta-[version].vsix`

## 支持的模式
- **Code模式**: 智能代码生成和补全
- **Architect模式**: 架构设计和代码结构优化
- **Ask模式**: 技术问题问答
- **Debug模式**: 代码调试和错误分析

## 安装步骤
1. 下载 .vsix 文件
2. 打开 VS Code
3. 按 Ctrl+Shift+P 打开命令面板
4. 输入 "Extensions: Install from VSIX"
5. 选择下载的文件进行安装
6. 重启 VS Code

## 更新说明
- 插件会定期更新以增强功能
- 建议及时更新以获得最佳体验
- 支持多种编辑器：VS Code、Cursor、Windsurf等

---
*最后更新: 2025-01* 