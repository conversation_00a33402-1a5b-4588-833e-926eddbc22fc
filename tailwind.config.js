/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./public/**/*.html",
    "./src/**/*.{vue,svelte}",
  ],
  
  darkMode: 'class',
  
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        secondary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
      },
      
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        '144': '36rem',
      },
      
      fontFamily: {
        sans: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Oxygen',
          'Ubuntu',
          'Cantarell',
          'Open Sans',
          'Helvetica Neue',
          'sans-serif'
        ],
        mono: [
          'SF Mono',
          'Monaco',
          'Inconsolata',
          'Roboto Mono',
          'source-code-pro',
          'Menlo',
          'Consolas',
          'monospace'
        ]
      },
      
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
        'spin-slow': 'spin 3s linear infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        }
      },
      
      boxShadow: {
        'glow-sm': '0 0 5px rgb(59 130 246 / 0.5)',
        'glow-md': '0 0 10px rgb(59 130 246 / 0.5)',
        'glow-lg': '0 0 20px rgb(59 130 246 / 0.5)',
        'glow-cyan': '0 0 20px rgb(6 182 212 / 0.5)',
      },
      
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'mesh-gradient': `
          radial-gradient(at 40% 20%, hsla(28,100%,74%,1) 0px, transparent 50%),
          radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%),
          radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%),
          radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%),
          radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%),
          radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%),
          radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%)
        `
      },
    },
  },
  
  plugins: [
         // require('@tailwindcss/forms'),
     // require('@tailwindcss/typography'), 
     // require('@tailwindcss/aspect-ratio'),
    
    function({ addUtilities, addComponents, theme }) {
      addUtilities({
        '.text-gradient': {
          'background-clip': 'text',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        },
        
        '.glass': {
          'backdrop-filter': 'blur(10px)',
          '-webkit-backdrop-filter': 'blur(10px)',
          'background': 'rgba(255, 255, 255, 0.1)',
          'border': '1px solid rgba(255, 255, 255, 0.2)',
        },
        
        '.gpu': {
          'transform': 'translateZ(0)',
          'will-change': 'transform',
        },
        
        '.scrollbar-none': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        },
        
        '.text-truncate': {
          'overflow': 'hidden',
          'text-overflow': 'ellipsis',
          'white-space': 'nowrap',
        },
        
        '.img-crisp': {
          'image-rendering': 'crisp-edges',
        }
      });
      
      addComponents({
        '.btn': {
          'padding': `${theme('spacing.2')} ${theme('spacing.4')}`,
          'border-radius': theme('borderRadius.md'),
          'font-weight': theme('fontWeight.medium'),
          'transition': 'all 0.2s ease-in-out',
          'cursor': 'pointer',
          'display': 'inline-flex',
          'align-items': 'center',
          'justify-content': 'center',
          'gap': theme('spacing.2'),
          
          '&:disabled': {
            'opacity': '0.5',
            'cursor': 'not-allowed',
          }
        },
        
        '.btn-primary': {
          'background': `linear-gradient(135deg, ${theme('colors.blue.500')}, ${theme('colors.cyan.500')})`,
          'color': theme('colors.white'),
          'border': 'none',
          
          '&:hover:not(:disabled)': {
            'background': `linear-gradient(135deg, ${theme('colors.blue.600')}, ${theme('colors.cyan.600')})`,
            'transform': 'translateY(-1px)',
            'box-shadow': theme('boxShadow.lg'),
          }
        },
        
        '.card': {
          'background': theme('colors.white'),
          'border-radius': theme('borderRadius.lg'),
          'box-shadow': theme('boxShadow.md'),
          'padding': theme('spacing.6'),
          'transition': 'all 0.2s ease-in-out',
          
          '&:hover': {
            'box-shadow': theme('boxShadow.lg'),
            'transform': 'translateY(-2px)',
          }
        },
        
        '.input': {
          'width': '100%',
          'padding': `${theme('spacing.3')} ${theme('spacing.4')}`,
          'border': `1px solid ${theme('colors.gray.300')}`,
          'border-radius': theme('borderRadius.md'),
          'background': theme('colors.white'),
          'font-size': theme('fontSize.sm'),
          'transition': 'all 0.2s ease-in-out',
          
          '&:focus': {
            'outline': 'none',
            'border-color': theme('colors.blue.500'),
            'box-shadow': `0 0 0 3px ${theme('colors.blue.500')}20`,
          }
        }
      });
    }
  ],
  
  corePlugins: {
    preflight: true,
    container: true,
    accessibility: true,
    
    float: false,
    clear: false,
    skew: false,
    caretColor: false,
    sepia: false,
    hueRotate: false,
    saturate: false,
    contrast: false,
    brightness: false,
    blur: false,
    dropShadow: false,
  },
  
  safelist: [
    'animate-pulse',
    'animate-spin',
    'animate-bounce',
    {
      pattern: /(bg|text|border)-(red|green|blue|yellow|purple|pink|gray)-(100|200|300|400|500|600|700|800|900)/,
    },
    {
      pattern: /(p|m|w|h)-(0|1|2|3|4|5|6|8|10|12|16|20|24|32|40|48|56|64)/,
    }
  ],
}; 