#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const glob = require('glob');

/**
 * 图片优化脚本
 * 生成WebP格式和多尺寸响应式图片
 */

class ImageOptimizer {
  constructor() {
    this.inputDir = 'public';
    this.outputDir = 'public/optimized';
    this.supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
    this.sizes = [320, 640, 1024, 1920];
    this.quality = {
      webp: 80,
      avif: 70,
      jpeg: 85,
      png: 95
    };
    this.optimizedImages = [];
    this.errors = [];
  }

  // 确保输出目录存在
  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
    
    // 创建子目录
    ['webp', 'avif', 'responsive'].forEach(subDir => {
      const fullPath = path.join(this.outputDir, subDir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    });
  }

  // 扫描图片文件
  scanImages() {
    console.log('🔍 扫描图片文件...');
    
    const patterns = this.supportedFormats.map(format => 
      `${this.inputDir}/**/*.${format}`
    );
    
    let allImages = [];
    patterns.forEach(pattern => {
      const files = glob.sync(pattern, {
        ignore: [`${this.outputDir}/**/*`] // 忽略输出目录
      });
      allImages = allImages.concat(files);
    });
    
    console.log(`📁 找到 ${allImages.length} 个图片文件`);
    return allImages;
  }

  // 优化单个图片
  async optimizeImage(imagePath) {
    try {
      const filename = path.basename(imagePath, path.extname(imagePath));
      const ext = path.extname(imagePath).toLowerCase();
      
      console.log(`🖼️  处理: ${imagePath}`);
      
      // 读取原始图片信息
      const image = sharp(imagePath);
      const metadata = await image.metadata();
      
      const results = {
        original: imagePath,
        formats: {},
        responsive: {},
        size: metadata.width + 'x' + metadata.height,
        originalSize: fs.statSync(imagePath).size
      };

      // 跳过SVG和小图片的响应式处理
      if (ext === '.svg' || metadata.width < 400) {
        console.log(`   ⏭️  跳过小图片或SVG: ${filename}`);
        return results;
      }

      // 生成WebP格式
      const webpPath = path.join(this.outputDir, 'webp', `${filename}.webp`);
      await image
        .webp({ quality: this.quality.webp, effort: 6 })
        .toFile(webpPath);
      results.formats.webp = webpPath;

      // 生成AVIF格式 (如果sharp支持)
      try {
        const avifPath = path.join(this.outputDir, 'avif', `${filename}.avif`);
        await image
          .avif({ quality: this.quality.avif, effort: 9 })
          .toFile(avifPath);
        results.formats.avif = avifPath;
      } catch (avifError) {
        console.log(`   ⚠️  AVIF格式不支持: ${filename}`);
      }

      // 生成响应式尺寸
      for (const size of this.sizes) {
        if (size >= metadata.width) continue; // 跳过大于原图的尺寸
        
        const responsivePath = path.join(
          this.outputDir, 
          'responsive', 
          `${filename}_${size}w${ext}`
        );
        
        await image
          .resize(size, null, { 
            withoutEnlargement: true,
            fit: 'inside'
          })
          .jpeg({ quality: this.quality.jpeg })
          .toFile(responsivePath);
        
        results.responsive[size] = responsivePath;

        // 同时生成WebP响应式版本
        const responsiveWebpPath = path.join(
          this.outputDir, 
          'responsive', 
          `${filename}_${size}w.webp`
        );
        
        await image
          .resize(size, null, { 
            withoutEnlargement: true,
            fit: 'inside'
          })
          .webp({ quality: this.quality.webp })
          .toFile(responsiveWebpPath);
        
        results.responsive[`${size}_webp`] = responsiveWebpPath;
      }

      // 计算压缩率
      if (results.formats.webp) {
        const webpSize = fs.statSync(results.formats.webp).size;
        const savings = ((results.originalSize - webpSize) / results.originalSize * 100).toFixed(1);
        results.webpSavings = `${savings}%`;
      }

      this.optimizedImages.push(results);
      console.log(`   ✅ 完成: ${filename} (WebP节省 ${results.webpSavings || 'N/A'})`);
      
      return results;

    } catch (error) {
      console.error(`   ❌ 优化失败: ${imagePath}`, error.message);
      this.errors.push({ path: imagePath, error: error.message });
      return null;
    }
  }

  // 批量优化
  async optimizeAll() {
    console.log('🚀 开始图片优化...\n');

    this.ensureOutputDir();
    const images = this.scanImages();
    
    if (images.length === 0) {
      console.log('📭 未找到需要优化的图片');
      return;
    }

    const startTime = Date.now();
    
    // 并发处理，但限制并发数避免内存溢出
    const concurrency = 3;
    const chunks = [];
    
    for (let i = 0; i < images.length; i += concurrency) {
      chunks.push(images.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      await Promise.all(chunk.map(imagePath => this.optimizeImage(imagePath)));
    }

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // 生成报告
    this.generateReport(duration);
  }

  // 生成报告
  generateReport(duration) {
    console.log('\n📊 === 图片优化报告 ===\n');

    const successful = this.optimizedImages.length;
    const failed = this.errors.length;
    const total = successful + failed;

    console.log(`📈 处理统计:`);
    console.log(`   • 总文件数: ${total}`);
    console.log(`   • 成功优化: ${successful}`);
    console.log(`   • 失败: ${failed}`);
    console.log(`   • 耗时: ${duration}秒\n`);

    if (successful > 0) {
      console.log('✅ 成功优化的图片:');
      this.optimizedImages.forEach(img => {
        console.log(`   • ${path.basename(img.original)} (${img.size})`);
        console.log(`     WebP: ${img.webpSavings || 'N/A'} 节省`);
        console.log(`     格式: ${Object.keys(img.formats).join(', ')}`);
        console.log(`     响应式: ${Object.keys(img.responsive).length} 个尺寸`);
      });
    }

    if (failed > 0) {
      console.log('\n❌ 优化失败的图片:');
      this.errors.forEach(error => {
        console.log(`   • ${error.path}: ${error.error}`);
      });
    }

    // 计算总体节省
    const totalOriginalSize = this.optimizedImages.reduce((sum, img) => sum + img.originalSize, 0);
    const totalWebpSize = this.optimizedImages.reduce((sum, img) => {
      if (img.formats.webp) {
        return sum + fs.statSync(img.formats.webp).size;
      }
      return sum;
    }, 0);

    if (totalOriginalSize > 0) {
      const totalSavings = ((totalOriginalSize - totalWebpSize) / totalOriginalSize * 100).toFixed(1);
      console.log(`\n💾 总计节省: ${totalSavings}% (${this.formatBytes(totalOriginalSize - totalWebpSize)})`);
    }

    // 生成映射文件
    this.generateImageMap();

    console.log('\n✅ 图片优化完成！');
  }

  // 生成图片映射文件
  generateImageMap() {
    const imageMap = {
      timestamp: new Date().toISOString(),
      images: this.optimizedImages.map(img => ({
        original: img.original,
        webp: img.formats.webp ? path.relative('public', img.formats.webp) : null,
        avif: img.formats.avif ? path.relative('public', img.formats.avif) : null,
        responsive: Object.entries(img.responsive).reduce((acc, [size, filePath]) => {
          acc[size] = path.relative('public', filePath);
          return acc;
        }, {}),
        savings: img.webpSavings
      }))
    };

    const mapPath = 'public/image-optimization-map.json';
    fs.writeFileSync(mapPath, JSON.stringify(imageMap, null, 2));
    console.log(`\n🗺️  图片映射已保存到: ${mapPath}`);
  }

  // 格式化字节大小
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 检查Sharp是否可用
function checkSharpAvailability() {
  try {
    const sharp = require('sharp');
    return true;
  } catch (error) {
    console.error('❌ Sharp 未安装，无法进行图片优化');
    console.log('💡 请运行以下命令安装 Sharp:');
    console.log('   npm install sharp --save-dev');
    console.log('   或');
    console.log('   yarn add sharp --dev');
    return false;
  }
}

// 运行优化
if (require.main === module) {
  if (checkSharpAvailability()) {
    const optimizer = new ImageOptimizer();
    optimizer.optimizeAll().catch(console.error);
  }
}

module.exports = ImageOptimizer; 