// LDAP配置管理模块
/* eslint-env node */
/* eslint-disable no-undef */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class LDAPConfigManager {
  constructor() {
    this.config = null;
    this.loadConfig();
  }

  // 加载配置
  loadConfig() {
    try {
      // 0. 加载环境变量文件
      this.loadEnvironmentFile();

      // 1. 从配置文件加载
      const configPath = path.join(__dirname, '../../ldap-config.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        this.config = JSON.parse(configData);
        console.log('✅ LDAP配置文件加载成功');
      } else {
        console.warn('⚠️  LDAP配置文件不存在，使用默认配置');
        this.config = this.getDefaultConfig();
      }

      // 2. 环境变量覆盖
      this.applyEnvironmentOverrides();

      console.log(`🔧 当前LDAP环境: ${this.getCurrentEnvironment()}`);
      
    } catch (error) {
      console.error('❌ LDAP配置加载失败:', error.message);
      console.log('🔄 使用默认配置');
      this.config = this.getDefaultConfig();
    }
  }

  // 加载环境变量文件
  loadEnvironmentFile() {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const envFiles = [
      `.env.${nodeEnv}`, // 优先加载特定环境文件
      '.env'             // 备选通用环境文件
    ];

    for (const envFile of envFiles) {
      const envPath = path.join(__dirname, '../../', envFile);
      if (fs.existsSync(envPath)) {
        console.log(`📄 加载环境变量文件: ${envFile}`);
        
        // 手动解析.env文件
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envLines = envContent.split('\n');
        
        for (const line of envLines) {
          const trimmedLine = line.trim();
          if (trimmedLine && !trimmedLine.startsWith('#') && trimmedLine.includes('=')) {
            const [key, ...valueParts] = trimmedLine.split('=');
            const value = valueParts.join('=').trim();
            
            // 移除引号
            const cleanValue = value.replace(/^["']|["']$/g, '');
            
            // 只有当环境变量未设置时才设置
            if (!process.env[key.trim()]) {
              process.env[key.trim()] = cleanValue;
            }
          }
        }
        break; // 找到文件后停止查找
      }
    }
  }

  // 默认配置
  getDefaultConfig() {
    const defaultHost = process.env.LDAP_DEFAULT_HOST || 'localhost';
    const defaultPort = process.env.LDAP_DEFAULT_PORT || '389';
    
    // 检查是否有环境变量定义的环境
    const hasEnvDefinedEnvironments = Object.keys(process.env).some(key => 
      key.startsWith('LDAP_') && key.endsWith('_URL')
    );
    
    return {
      // 如果有环境变量定义的环境，就不创建默认环境，避免重复
      environments: hasEnvDefinedEnvironments ? {} : {
        devvdi: {
          url: `ldap://${defaultHost}:${defaultPort}`,
          baseDN: 'dc=devvdi,dc=ynrcc,dc=com',
          bindDN: '',
          bindPassword: '',
          userSearchBase: 'dc=devvdi,dc=ynrcc,dc=com',
          userFilter: '(cn={{username}})',
          name: '240.10云桌面环境'
        }
      },
      settings: {
        defaultEnvironment: process.env.LDAP_DEFAULT_ENVIRONMENT || 'devvdi',
        connectionTimeout: parseInt(process.env.LDAP_CONNECTION_TIMEOUT || '5000', 10),
        searchTimeout: parseInt(process.env.LDAP_SEARCH_TIMEOUT || '10000', 10),
        maxRetries: parseInt(process.env.LDAP_MAX_RETRIES || '3', 10)
      }
    };
  }

  // 应用环境变量覆盖
  applyEnvironmentOverrides() {
    const envMappings = {
      'LDAP_ENVIRONMENT': 'settings.defaultEnvironment',
      'LDAP_CONNECTION_TIMEOUT': 'settings.connectionTimeout',
      'LDAP_SEARCH_TIMEOUT': 'settings.searchTimeout',
      'LDAP_MAX_RETRIES': 'settings.maxRetries'
    };

    // 应用通用设置覆盖
    Object.keys(envMappings).forEach(envKey => {
      const envValue = process.env[envKey];
      if (envValue) {
        const configPath = envMappings[envKey].split('.');
        if (configPath.length === 2) {
          const [section, key] = configPath;
          if (this.config[section]) {
            // 类型转换
            let value = envValue;
            if (key.includes('Timeout') || key.includes('Retries')) {
              value = parseInt(envValue, 10);
            }
            this.config[section][key] = value;
            console.log(`🔧 环境变量覆盖: ${envKey} = ${value}`);
          }
        }
      }
    });

    // 从环境变量动态构建环境配置
    this.buildEnvironmentConfigsFromEnv();
  }

  // 从环境变量构建环境配置
  buildEnvironmentConfigsFromEnv() {
    const envPrefixes = {};
    
    // 首先找出所有环境名（通过URL后缀）
    const environmentNames = new Set();
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('LDAP_') && key.endsWith('_URL')) {
        const envName = key.substring(5, key.length - 4);
        environmentNames.add(envName);
      }
    });

    console.log('🔍 找到的环境名称:', Array.from(environmentNames));

    // 扫描所有环境变量，找出LDAP环境配置
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('LDAP_')) {
        // 跳过全局配置
        if (['LDAP_DEFAULT_HOST', 'LDAP_DEFAULT_PORT', 'LDAP_DEFAULT_ENVIRONMENT', 'LDAP_AUTH_PORT', 'LDAP_CONNECTION_TIMEOUT', 'LDAP_SEARCH_TIMEOUT', 'LDAP_MAX_RETRIES', 'LDAP_TEST_PORT', 'LDAP_GC_PORT'].includes(key)) {
          return;
        }

        // 尝试匹配每个已知的环境名
        for (const envName of environmentNames) {
          const envPrefix = `LDAP_${envName}_`;
          if (key.startsWith(envPrefix)) {
            const configKey = key.substring(envPrefix.length);
            
            if (!envPrefixes[envName]) {
              envPrefixes[envName] = {};
            }
            envPrefixes[envName][configKey] = process.env[key];
            
            console.log(`🔧 解析环境配置: ${envName}.${configKey} = ${process.env[key]}`);
            break;
          }
        }
      }
    });

    // 为每个找到的环境创建配置
    Object.keys(envPrefixes).forEach(envName => {
      const envConfig = envPrefixes[envName];
      
      // 如果有URL配置，说明这是一个有效的环境
      if (envConfig.URL) {
        if (!this.config.environments) {
          this.config.environments = {};
        }

        // 构建完整的环境配置
        this.config.environments[envName] = {
          url: envConfig.URL,
          baseDN: envConfig.BASE_DN || '',
          bindDN: envConfig.BIND_DN || '',
          bindPassword: envConfig.BIND_PASSWORD || '',
          userSearchBase: envConfig.USER_SEARCH_BASE || envConfig.BASE_DN || '',
          userFilter: envConfig.USER_FILTER || '(cn={{username}})',
          userDNPattern: envConfig.USER_DN_PATTERN || '',
          userDomain: envConfig.USER_DOMAIN || '',
          useDirectBind: envConfig.USE_DIRECT_BIND === 'true' || false,
          name: envConfig.NAME || envName,
          description: envConfig.DESCRIPTION || `${envName} LDAP环境`,
          enabled: envConfig.ENABLED !== 'false' // 默认启用，除非明确设置为false
        };
        
        console.log(`🔧 从环境变量构建LDAP环境: ${envName} (${envConfig.NAME || envName})`);
      }
    });
  }

  // 获取当前环境
  getCurrentEnvironment() {
    return process.env.LDAP_ENVIRONMENT || process.env.LDAP_DEFAULT_ENVIRONMENT || this.config?.settings?.defaultEnvironment || 'devvdi';
  }

  // 获取当前环境配置
  getCurrentConfig() {
    const currentEnv = this.getCurrentEnvironment();
    const envConfig = this.config?.environments?.[currentEnv];
    
    if (!envConfig) {
      throw new Error(`LDAP环境 '${currentEnv}' 配置不存在`);
    }

    return {
      ...envConfig,
      timeout: this.config.settings.connectionTimeout,
      searchTimeout: this.config.settings.searchTimeout,
      maxRetries: this.config.settings.maxRetries
    };
  }

  // 获取所有环境列表
  getAllEnvironments() {
    if (!this.config?.environments) {
      return [];
    }

    const environments = Object.keys(this.config.environments).map(key => ({
      id: key,
      name: this.config.environments[key].name,
      url: this.config.environments[key].url,
      baseDN: this.config.environments[key].baseDN,
      description: this.config.environments[key].description,
      isActive: key === this.getCurrentEnvironment()
    }));

    // 根据环境名称去重，优先保留带_ENV后缀的环境（更具体的环境配置）
    const uniqueEnvironments = [];
    const seenNames = new Set();
    
    // 首先添加带_ENV后缀的环境
    environments
      .filter(env => env.id.endsWith('_ENV'))
      .forEach(env => {
        if (!seenNames.has(env.name)) {
          uniqueEnvironments.push(env);
          seenNames.add(env.name);
          console.log(`🔧 保留环境: ${env.id} (${env.name})`);
        }
      });
    
    // 然后添加不重复的其他环境
    environments
      .filter(env => !env.id.endsWith('_ENV'))
      .forEach(env => {
        if (!seenNames.has(env.name)) {
          uniqueEnvironments.push(env);
          seenNames.add(env.name);
          console.log(`🔧 保留环境: ${env.id} (${env.name})`);
        } else {
          console.log(`🔧 跳过重复环境: ${env.id} (${env.name})`);
        }
      });

    return uniqueEnvironments;
  }

  // 验证环境是否存在
  isValidEnvironment(envId) {
    return !!(this.config?.environments?.[envId]);
  }

  // 获取环境配置
  getEnvironmentConfig(envId) {
    if (!this.isValidEnvironment(envId)) {
      throw new Error(`LDAP环境 '${envId}' 不存在`);
    }

    return {
      ...this.config.environments[envId],
      timeout: this.config.settings.connectionTimeout,
      searchTimeout: this.config.settings.searchTimeout,
      maxRetries: this.config.settings.maxRetries
    };
  }

  // 重新加载配置
  reloadConfig() {
    console.log('🔄 重新加载LDAP配置...');
    this.loadConfig();
    return this.config;
  }

  // 获取脱敏的配置信息
  getSanitizedConfig() {
    const config = this.getCurrentConfig();
    return {
      server: config.url,
      baseDN: config.baseDN,
      userSearchBase: config.userSearchBase,
      userFilter: config.userFilter,
      environment: config.name,
      hasBindUser: !!(config.bindDN && config.bindPassword),
      timeout: config.timeout,
      searchTimeout: config.searchTimeout
    };
  }
}

// 创建单例实例
const ldapConfigManager = new LDAPConfigManager();

export default ldapConfigManager; 