<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema.org 标准参考 - YNNX AI Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 10px;
        }
        h2 {
            color: #1e40af;
            margin-top: 30px;
        }
        .schema-type {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .property {
            background: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 10px;
            margin: 10px 0;
        }
        code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .example {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .note {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Schema.org 标准参考</h1>
        
        <div class="note">
            <strong>📌 内网版本说明：</strong> 本文档为 Schema.org 标准的本地化参考，适用于内网环境下的开发工作。
        </div>

        <h2>🎯 什么是 Schema.org</h2>
        <p>Schema.org 是一个协作性的、社区驱动的活动，其使命是创建、维护和推广结构化数据的模式，用于互联网、网页、电子邮件消息等。</p>

        <h2>🔧 常用类型</h2>

        <div class="schema-type">
            <h3>Organization（组织）</h3>
            <p>用于描述组织、公司或机构的信息。</p>
            <div class="property">
                <strong>主要属性：</strong>
                <ul>
                    <li><code>name</code> - 组织名称</li>
                    <li><code>url</code> - 官方网站</li>
                    <li><code>logo</code> - 组织标志</li>
                    <li><code>address</code> - 地址信息</li>
                    <li><code>contactPoint</code> - 联系方式</li>
                </ul>
            </div>
        </div>

        <div class="schema-type">
            <h3>WebSite（网站）</h3>
            <p>用于描述网站的基本信息和搜索功能。</p>
            <div class="property">
                <strong>主要属性：</strong>
                <ul>
                    <li><code>name</code> - 网站名称</li>
                    <li><code>url</code> - 网站URL</li>
                    <li><code>potentialAction</code> - 搜索功能</li>
                    <li><code>publisher</code> - 发布者</li>
                </ul>
            </div>
        </div>

        <div class="schema-type">
            <h3>SoftwareApplication（软件应用）</h3>
            <p>用于描述软件应用程序的信息。</p>
            <div class="property">
                <strong>主要属性：</strong>
                <ul>
                    <li><code>name</code> - 应用名称</li>
                    <li><code>applicationCategory</code> - 应用类别</li>
                    <li><code>operatingSystem</code> - 支持的操作系统</li>
                    <li><code>offers</code> - 价格信息</li>
                </ul>
            </div>
        </div>

        <h2>💡 实施示例</h2>

        <div class="example">
            <h4>JSON-LD 格式示例：</h4>
            <pre><code>{
  "@context": "/local-resources/schema-org-context.json",
  "@type": "Organization",
  "name": "云南农信科技结算中心",
  "url": "/",
  "logo": "/favicon.svg",
  "description": "YNNX AI 开发者平台"
}</code></pre>
        </div>

        <h2>🛠️ 在 YNNX AI Platform 中的应用</h2>
        <p>我们的平台使用 Schema.org 标准来：</p>
        <ul>
            <li>✅ 提供结构化的组织信息</li>
            <li>✅ 优化搜索引擎可见性</li>
            <li>✅ 标准化API文档格式</li>
            <li>✅ 改善用户体验</li>
        </ul>

        <div class="note">
            <strong>🔗 更多信息：</strong> 
            <p>由于内网环境限制，如需查看完整的 Schema.org 规范，请在有网络的环境下访问官方网站。</p>
            <p>本文档涵盖了平台开发中最常用的类型和属性。</p>
        </div>

        <h2>📚 相关资源</h2>
        <ul>
            <li><a href="/local-resources/api-reference.html">📖 API 参考文档</a></li>
            <li><a href="/local-resources/search-help.html">🔍 搜索帮助</a></li>
            <li><a href="/">🏠 返回主页</a></li>
        </ul>
    </div>
</body>
</html>
