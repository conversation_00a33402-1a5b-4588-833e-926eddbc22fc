#!/bin/bash

# YNNX AI Platform 一键部署脚本
set -e

echo "🚀 开始部署 YNNX AI Platform..."

# 检查必要的命令
command -v docker >/dev/null 2>&1 || { echo "❌ 请先安装 Docker"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "❌ 请先安装 Docker Compose"; exit 1; }

# 检查构建产物
if [ ! -d "dist" ]; then
    echo "❌ 未找到 dist 目录，请先运行 npm run build"
    exit 1
fi

# 检查环境配置
if [ ! -f ".env.production" ]; then
    echo "⚠️  未找到 .env.production 文件"
    echo "📝 请创建 .env.production 配置文件，参考："
    echo "   cp env.example .env.production"
    echo "   然后编辑 .env.production 文件配置实际参数"
    exit 1
fi

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p logs/nginx

# 停止现有服务（如果存在）
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# 构建并启动服务
echo "🏗️  构建并启动服务..."
docker-compose -f docker-compose.prod.yml up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost/health >/dev/null 2>&1; then
        echo "✅ 服务健康检查通过"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 服务健康检查失败"
        echo "📝 查看日志："
        docker-compose -f docker-compose.prod.yml logs --tail=50
        exit 1
    fi
    echo "   等待服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 部署完成！"
echo "📊 访问地址："
echo "   前端应用: http://localhost"
echo "   API服务: http://localhost:3001"
echo "   LDAP服务: http://localhost:3002"
echo ""
echo "📝 常用命令："
echo "   查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "   停止服务: docker-compose -f docker-compose.prod.yml down"
echo "   重启服务: docker-compose -f docker-compose.prod.yml restart"
echo "" 