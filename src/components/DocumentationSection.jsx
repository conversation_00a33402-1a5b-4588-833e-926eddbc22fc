import React, { useState, memo, useMemo, useCallback, useEffect } from 'react';
import { FaBook, FaQuestionCircle, FaSearch, FaExternalLinkAlt, FaCode, FaLaptopCode, FaTimes, FaCopy, FaDownload, FaCog, FaRocket, FaLightbulb } from 'react-icons/fa';
import { HiChevronRight, HiCheckCircle } from 'react-icons/hi';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import documentService from '../services/documentService';

// 引入代码高亮样式
import 'highlight.js/styles/github-dark.css';

const DocumentationSection = memo(() => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // 状态管理
  const [categories, setCategories] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [filteredDocs, setFilteredDocs] = useState([]);
  const [documentContent, setDocumentContent] = useState('');
  const [contentLoading, setContentLoading] = useState(false);

  // 图标映射
  const iconMap = {
    FaBook: <FaBook />,
    FaCode: <FaCode />,
    FaLaptopCode: <FaLaptopCode />,
    FaQuestionCircle: <FaQuestionCircle />
  };



  // 加载分类和初始文档
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // 加载分类
        const categoriesData = await documentService.getCategories();
        setCategories(categoriesData);
        
        // 加载初始文档列表
        const docsData = await documentService.getDocumentsByCategory('all');
        setDocuments(docsData);
        setFilteredDocs(docsData);
        
      } catch (err) {
        console.error('Failed to load documentation data:', err);
        setError('加载文档数据失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // 处理分类切换
  const handleCategoryChange = useCallback(async (categoryId) => {
    try {
      setActiveCategory(categoryId);
      setSearchQuery(''); // 切换分类时清空搜索
      
      const docsData = await documentService.getDocumentsByCategory(categoryId);
      setDocuments(docsData);
      setFilteredDocs(docsData);
    } catch (err) {
      console.error('Failed to load category documents:', err);
      setError('加载分类文档失败');
    }
  }, []);

  // 处理搜索
  useEffect(() => {
    const searchDocuments = async () => {
      try {
        if (!searchQuery.trim()) {
          setFilteredDocs(documents);
          return;
        }

        const searchResults = await documentService.searchDocuments(searchQuery, activeCategory);
        setFilteredDocs(searchResults);
      } catch (err) {
        console.error('Search failed:', err);
        // 搜索失败时回退到本地搜索
        const localResults = documents.filter(doc => {
          const searchableText = [
            doc.title,
            doc.description,
            ...(doc.tags || [])
          ].join(' ').toLowerCase();
          return searchableText.includes(searchQuery.toLowerCase());
        });
        setFilteredDocs(localResults);
      }
    };

    // 防抖搜索
    const timeoutId = setTimeout(searchDocuments, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery, documents, activeCategory]);

  // 打开文档模态框
  const openDocModal = useCallback(async (doc) => {
    try {
      setSelectedDoc(doc);
      setIsModalOpen(true);
      setContentLoading(true);
      setDocumentContent('');
      
      // 加载文档内容
      const content = await documentService.loadDocumentContent(doc.file);
      const processedContent = documentService.resolveMediaPath(content, doc.file);
      setDocumentContent(processedContent);
      
    } catch (err) {
      console.error('Failed to load document content:', err);
      setDocumentContent('# 文档加载失败\n\n抱歉，无法加载此文档内容。请稍后重试。');
    } finally {
      setContentLoading(false);
    }
  }, []);

  // 关闭文档模态框
  const closeDocModal = useCallback(() => {
    setSelectedDoc(null);
    setIsModalOpen(false);
    setDocumentContent('');
    setContentLoading(false);
  }, []);

  // 统计信息
  const stats = useMemo(() => {
    const totalDocs = documents.length;
    const pluginCount = categories.filter(cat => cat.id !== 'all').length;
    
    return {
      totalDocs,
      pluginCount,
      totalReadTime: documents.reduce((total, doc) => {
        const time = parseInt(doc.readTime?.replace(/[^\d]/g, '') || '0');
        return total + time;
      }, 0)
    };
  }, [documents, categories]);

  // 文档卡片组件
  const DocCard = memo(({ item }) => (
    <div
      className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 hover:border-cyan-500/50 transition-all duration-300 cursor-pointer group"
      onClick={() => openDocModal(item)}
    >
      <div className="flex items-start justify-between mb-4">
        <h3 className="text-xl font-semibold text-white group-hover:text-cyan-400 transition-colors">
          {item.title}
        </h3>
        <div className="flex flex-col items-end gap-2">
          <span className="text-xs bg-cyan-500/20 text-cyan-400 px-2 py-1 rounded">
            {item.readTime}
          </span>
          {item.category && (
            <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-1 rounded">
              {item.category}
            </span>
          )}
        </div>
      </div>

      <p className="text-gray-400 mb-4 line-clamp-2">{item.description}</p>
      
      {/* 标签 */}
      {item.tags && item.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {item.tags.slice(0, 3).map((tag, idx) => (
            <span key={idx} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
              #{tag}
            </span>
          ))}
          {item.tags.length > 3 && (
            <span className="text-xs text-gray-500">
              +{item.tags.length - 3}
            </span>
          )}
        </div>
      )}

      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center gap-2 text-cyan-400 group-hover:text-cyan-300 transition-colors">
          <FaExternalLinkAlt size={12} />
          <span className="text-sm">查看详情</span>
        </div>
        <HiChevronRight className="text-gray-500 group-hover:text-cyan-400 transition-colors" />
      </div>
    </div>
  ));

  // 错误状态
  if (error) {
    return (
      <section id="docs" className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="text-red-400 text-xl mb-4">
            <FaTimes size={48} className="mx-auto mb-4" />
            {error}
          </div>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-cyan-500/20 text-cyan-400 rounded-lg hover:bg-cyan-500/30 transition-colors"
          >
            重新加载
          </button>
        </div>
      </section>
    );
  }

  return (
    <section id="docs" className="py-20 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题部分 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full px-6 py-3 mb-6">
            <FaBook className="text-blue-400" />
            <span className="text-blue-300 font-medium">文档中心</span>
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            <i className="fas fa-book text-cyan-400 mr-3"></i>开发者文档
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            完整的安装指南、配置教程和使用文档，帮助您快速上手AI开发工具
          </p>
        </div>

        {/* 统计信息 */}
        {!loading && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
            <div className="text-center p-4 bg-gray-800/30 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-cyan-400">{stats.totalDocs}</div>
              <div className="text-sm text-gray-400">总文档数</div>
            </div>
            <div className="text-center p-4 bg-gray-800/30 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-green-400">{stats.pluginCount}</div>
              <div className="text-sm text-gray-400">插件支持</div>
            </div>
            <div className="text-center p-4 bg-gray-800/30 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-purple-400">{filteredDocs.length}</div>
              <div className="text-sm text-gray-400">当前显示</div>
            </div>
            <div className="text-center p-4 bg-gray-800/30 rounded-lg border border-gray-700">
              <div className="text-2xl font-bold text-yellow-400">{stats.totalReadTime}</div>
              <div className="text-sm text-gray-400">总阅读时长(分钟)</div>
            </div>
          </div>
        )}

        {/* 分类导航 */}
        {!loading && (
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryChange(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg shadow-cyan-500/25'
                    : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700/50 border border-gray-700'
                }`}
              >
                {iconMap[category.icon] || <FaBook />}
                <span className="font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        )}

        {/* 搜索框 */}
        {!loading && (
          <div className="max-w-md mx-auto mb-12">
            <div className="relative">
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文档内容、标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-gray-800/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-cyan-500/50 transition-colors"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <FaTimes />
                </button>
              )}
            </div>
            {searchQuery && (
              <div className="mt-2 text-sm text-gray-400 text-center">
                {filteredDocs.length > 0 ? `找到 ${filteredDocs.length} 个相关文档` : '未找到相关文档'}
              </div>
            )}
          </div>
        )}
          
        {/* 文档列表 */}
        {loading ? (
          <div className="text-center py-20">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
            <p className="text-gray-400 mt-4">加载文档中...</p>
          </div>
        ) : filteredDocs.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDocs.map((item, index) => (
              <DocCard key={`${item.category}-${item.id}-${index}`} item={item} />
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4"><FaSearch className="text-gray-500 mx-auto" /></div>
            <h3 className="text-xl font-semibold text-white mb-2">
              没有找到相关文档
            </h3>
            <p className="text-gray-400">
              尝试使用其他关键词搜索或选择不同的分类
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setActiveCategory('all');
              }}
              className="mt-4 px-4 py-2 bg-cyan-500/20 text-cyan-400 rounded-lg hover:bg-cyan-500/30 transition-colors"
            >
              重置搜索
            </button>
          </div>
        )}
      </div>

      {/* 文档详情模态框 */}
      {isModalOpen && selectedDoc && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={closeDocModal}
        >
          <div
            className="bg-gray-900 border border-gray-700 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 模态框头部 */}
            <div className="sticky top-0 bg-gray-900 border-b border-gray-700 p-6 z-10">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">{selectedDoc.title}</h2>
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <span><i className="fas fa-book-open mr-1"></i>{selectedDoc.readTime}</span>
                    <span><i className="fas fa-folder mr-1"></i>{selectedDoc.category}</span>
                  </div>
                </div>
                <button
                  onClick={closeDocModal}
                  className="text-gray-400 hover:text-white transition-colors p-2"
                >
                  <FaTimes size={20} />
                </button>
              </div>
              
              {/* 标签 */}
              {selectedDoc.tags && selectedDoc.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-4">
                  {selectedDoc.tags.map((tag, idx) => (
                    <span key={idx} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* 模态框内容 */}
            <div className="p-6">
              {contentLoading ? (
                <div className="text-center py-20">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
                  <p className="text-gray-400 mt-4">加载文档内容中...</p>
                </div>
              ) : (
                <div className="prose prose-invert prose-cyan max-w-none">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeHighlight, rehypeRaw]}
                    components={{
                      // 自定义代码块渲染
                      code: ({ node, inline, className, children, ...props }) => {
                        if (inline) {
                          return (
                            <code className="bg-gray-800 text-cyan-400 px-1 py-0.5 rounded text-sm" {...props}>
                              {children}
                            </code>
                          );
                        }
                        return (
                          <div className="relative">
                            <code className={className} {...props}>
                              {children}
                            </code>
                          </div>
                        );
                      },
                      // 自定义表格渲染
                      table: ({ children }) => (
                        <div className="overflow-x-auto">
                          <table className="min-w-full border border-gray-700 rounded-lg">
                            {children}
                          </table>
                        </div>
                      ),
                      // 自定义视频渲染
                      video: ({ src, ...props }) => (
                        <video 
                          controls 
                          className="w-full max-w-3xl mx-auto my-6 rounded-lg"
                          src={src}
                          {...props}
                        />
                      )
                    }}
                  >
                    {documentContent}
                  </ReactMarkdown>
                </div>
              )}

              {/* 底部操作 */}
              <div className="mt-8 pt-6 border-t border-gray-700 flex justify-center">
                <button
                  onClick={closeDocModal}
                  className="px-6 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 text-black rounded-lg font-medium hover:shadow-lg transition-all"
                >
                  我知道了
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
});

DocumentationSection.displayName = 'DocumentationSection';

export default DocumentationSection;