#!/usr/bin/env node

/**
 * 本地化外部资源脚本
 * 自动下载外部资源到本地并替换所有外部链接
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

console.log('🌐 开始本地化外部资源...\n');

// 创建本地资源目录
const localResourcesDir = 'public/local-resources';
if (!fs.existsSync(localResourcesDir)) {
  fs.mkdirSync(localResourcesDir, { recursive: true });
}

// 下载文件的通用函数
function downloadFile(url, localPath) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    const file = fs.createWriteStream(localPath);
    
    client.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          resolve(localPath);
        });
      } else if (response.statusCode === 301 || response.statusCode === 302) {
        // 处理重定向
        downloadFile(response.headers.location, localPath).then(resolve).catch(reject);
      } else {
        reject(new Error(`下载失败: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      fs.unlink(localPath, () => {}); // 删除部分下载的文件
      reject(err);
    });
  });
}

// 1. 处理浏览器兼容性组件
console.log('📝 处理浏览器兼容性组件...');
const browserCompatibilityPath = 'src/utils/browserCompatibility.js';

if (fs.existsSync(browserCompatibilityPath)) {
  let content = fs.readFileSync(browserCompatibilityPath, 'utf-8');
  
  // 备份原文件
  fs.writeFileSync(browserCompatibilityPath + '.backup', content);
  
  // 替换外部下载链接
  const browserReplacements = {
    'https://www.google.com/chrome/': '/local-resources/browser-info.html#chrome',
    'https://www.mozilla.org/firefox/': '/local-resources/browser-info.html#firefox',
    'https://www.microsoft.com/edge': '/local-resources/browser-info.html#edge',
    'https://www.apple.com/safari/': '/local-resources/browser-info.html#safari'
  };
  
  Object.entries(browserReplacements).forEach(([oldUrl, newUrl]) => {
    content = content.replace(new RegExp(oldUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newUrl);
  });
  
  fs.writeFileSync(browserCompatibilityPath, content);
  console.log('✅ 已替换浏览器兼容性组件中的外部链接');
} else {
  console.log('⚠️  浏览器兼容性组件文件不存在，跳过');
}

// 2. 创建本地浏览器信息页面
console.log('\n📄 创建本地浏览器信息页面...');
const browserInfoHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器下载信息</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .browser-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .browser-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #0066cc; }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .version-info {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🌐 推荐浏览器信息</h1>
    
    <div class="note">
        <strong>📢 内网环境提示:</strong> 请联系IT部门获取以下浏览器的安装包，或通过企业软件中心下载。
    </div>

    <div id="chrome" class="browser-card">
        <h2>🟦 Google Chrome</h2>
        <div class="version-info">推荐版本: 70+ (当前最新版本推荐)</div>
        <p><strong>特点:</strong> 性能优秀，兼容性最佳，开发者工具强大</p>
        <p><strong>适用系统:</strong> Windows, macOS, Linux</p>
        <p><strong>获取方式:</strong> 联系IT部门或访问企业软件中心</p>
    </div>

    <div id="firefox" class="browser-card">
        <h2>🟧 Mozilla Firefox</h2>
        <div class="version-info">推荐版本: 65+ (当前最新版本推荐)</div>
        <p><strong>特点:</strong> 注重隐私保护，开源浏览器，扩展丰富</p>
        <p><strong>适用系统:</strong> Windows, macOS, Linux</p>
        <p><strong>获取方式:</strong> 联系IT部门或访问企业软件中心</p>
    </div>

    <div id="edge" class="browser-card">
        <h2>🟦 Microsoft Edge</h2>
        <div class="version-info">推荐版本: 79+ (基于Chromium)</div>
        <p><strong>特点:</strong> Windows系统集成度高，性能优秀</p>
        <p><strong>适用系统:</strong> Windows (系统自带), macOS, Linux</p>
        <p><strong>获取方式:</strong> Windows系统自带，其他系统联系IT部门</p>
    </div>

    <div id="safari" class="browser-card">
        <h2>🟦 Apple Safari</h2>
        <div class="version-info">推荐版本: 12+ (macOS系统自带)</div>
        <p><strong>特点:</strong> macOS系统优化，省电性能好</p>
        <p><strong>适用系统:</strong> macOS (系统自带)</p>
        <p><strong>获取方式:</strong> macOS系统自带，通过系统更新获取最新版本</p>
    </div>

    <div class="note">
        <strong>💡 提示:</strong> 
        <ul>
            <li>推荐优先使用 Chrome 或新版 Edge 浏览器</li>
            <li>确保浏览器版本不低于推荐版本</li>
            <li>定期更新浏览器以获得最佳体验和安全性</li>
            <li>如遇到兼容性问题，请联系技术支持</li>
        </ul>
    </div>

    <script>
        // 根据URL hash高亮对应浏览器
        if (window.location.hash) {
            const target = document.querySelector(window.location.hash);
            if (target) {
                target.style.border = '3px solid #0066cc';
                target.scrollIntoView({behavior: 'smooth'});
            }
        }
    </script>
</body>
</html>`;

fs.writeFileSync(path.join(localResourcesDir, 'browser-info.html'), browserInfoHtml);
console.log('✅ 已创建本地浏览器信息页面');

// 3. 处理index.html中的外部链接
console.log('\n📝 处理index.html中的外部链接...');
const indexPath = 'index.html';

if (fs.existsSync(indexPath)) {
  let indexContent = fs.readFileSync(indexPath, 'utf-8');
  
  // 备份原文件
  fs.writeFileSync(indexPath + '.backup', indexContent);
  
  // 替换浏览器下载链接
  const htmlReplacements = {
    'href="https://www.google.com/chrome/" target="_blank"': 'href="/local-resources/browser-info.html#chrome" target="_blank"',
    'href="https://www.mozilla.org/firefox/" target="_blank"': 'href="/local-resources/browser-info.html#firefox" target="_blank"',
    'href="https://www.microsoft.com/edge" target="_blank"': 'href="/local-resources/browser-info.html#edge" target="_blank"',
    'href="https://www.apple.com/safari/" target="_blank"': 'href="/local-resources/browser-info.html#safari" target="_blank"'
  };
  
  Object.entries(htmlReplacements).forEach(([oldLink, newLink]) => {
    indexContent = indexContent.replace(new RegExp(oldLink.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newLink);
  });
  
  fs.writeFileSync(indexPath, indexContent);
  console.log('✅ 已替换HTML中的外部链接');
}

// 4. 处理服务器配置文件
console.log('\n📝 处理服务器配置文件...');
const serverFiles = [
  
  'src/server/ldapAuthServer.js'
];

serverFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // 备份原文件
    fs.writeFileSync(filePath + '.backup', content);
    
    // 替换默认URL为本地地址
    content = content.replace(
      /return process\.env\.PROD_FRONTEND_URL \|\| 'https:\/\/ai\.ynnx\.com'/g,
      "return process.env.PROD_FRONTEND_URL || 'http://localhost:5173'"
    );
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ 已处理 ${filePath}`);
  }
});

// 5. 处理模拟数据中的示例URL
console.log('\n📝 处理模拟数据中的示例URL...');
const dataFiles = [
  
];

dataFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf-8');
    
    // 备份原文件（如果还没有备份）
    if (!fs.existsSync(filePath + '.backup')) {
      fs.writeFileSync(filePath + '.backup', content);
    }
    
    // 替换示例URL为本地说明
    const urlReplacements = {
      'https://ynnx.com': 'http://localhost:5173',
      'https://docs.ynnx.com/search?q={search_term_string}': '/local-resources/search-help.html',
      'https://docs.ynnx.com/api-reference': '/local-resources/api-reference.html',
  
      'https://github.com/ynnx/cli/releases/latest': '/local-resources/cli-download.html'
    };
    
    Object.entries(urlReplacements).forEach(([oldUrl, newUrl]) => {
      content = content.replace(new RegExp(oldUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newUrl);
    });
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ 已处理 ${filePath} 中的示例URL`);
  }
});

// 6. 创建本地帮助页面
console.log('\n📄 创建本地帮助页面...');

// 搜索帮助页面
const searchHelpHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索帮助</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px 20px; }
        .help-card { background: white; border-radius: 10px; padding: 30px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="help-card">
        <h1>🔍 搜索帮助</h1>
        <p>此功能在内网环境中暂不可用。如需帮助，请联系技术支持。</p>
        <button onclick="window.close()">关闭</button>
    </div>
</body>
</html>`;

// API参考页面
const apiReferenceHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API参考</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px 20px; }
        .help-card { background: white; border-radius: 10px; padding: 30px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="help-card">
        <h1>📚 API参考文档</h1>
        <p>API参考文档在内网环境中暂不可用。请联系技术支持获取离线文档。</p>
        <button onclick="window.close()">关闭</button>
    </div>
</body>
</html>`;



// CLI下载页面
const cliDownloadHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CLI工具下载</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px 20px; }
        .help-card { background: white; border-radius: 10px; padding: 30px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="help-card">
        <h1>⚡ CLI工具下载</h1>
        <p>CLI工具在内网环境中暂不可用。请联系技术支持获取离线安装包。</p>
        <button onclick="window.close()">关闭</button>
    </div>
</body>
</html>`;

// 保存帮助页面
fs.writeFileSync(path.join(localResourcesDir, 'search-help.html'), searchHelpHtml);
fs.writeFileSync(path.join(localResourcesDir, 'api-reference.html'), apiReferenceHtml);

fs.writeFileSync(path.join(localResourcesDir, 'cli-download.html'), cliDownloadHtml);

console.log('✅ 已创建所有本地帮助页面');

// 7. 处理FontAwesome注释中的链接
console.log('\n📝 处理FontAwesome注释...');
const fontAwesomePath = 'public/assets/fonts/fontawesome.css';
if (fs.existsSync(fontAwesomePath)) {
  let content = fs.readFileSync(fontAwesomePath, 'utf-8');
  
  // 备份原文件
  if (!fs.existsSync(fontAwesomePath + '.backup')) {
    fs.writeFileSync(fontAwesomePath + '.backup', content);
  }
  
  // 替换注释中的链接为本地说明
  content = content.replace(
    /\* Font Awesome Free 6\.5\.1 by @fontawesome - https:\/\/fontawesome\.com/g,
    '* Font Awesome Free 6.5.1 by @fontawesome - 本地化版本'
  );
  content = content.replace(
    /\* License - https:\/\/fontawesome\.com\/license\/free/g,
    '* License - 本地化版本，原始许可协议：CC BY 4.0, SIL OFL 1.1, MIT License'
  );
  
  fs.writeFileSync(fontAwesomePath, content);
  console.log('✅ 已处理FontAwesome注释');
}

// 8. 创建恢复脚本
console.log('\n📝 创建恢复脚本...');
const restoreScript = `#!/usr/bin/env node

/**
 * 恢复原始文件脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 恢复原始文件...');

const backupFiles = [
  'src/utils/browserCompatibility.js.backup',
  'index.html.backup',
  
  'src/server/ldapAuthServer.js.backup',
  'public/assets/fonts/fontawesome.css.backup'
];

backupFiles.forEach(backupFile => {
  if (fs.existsSync(backupFile)) {
    const originalFile = backupFile.replace('.backup', '');
    fs.copyFileSync(backupFile, originalFile);
    fs.unlinkSync(backupFile);
    console.log(\`✅ 已恢复 \${originalFile}\`);
  }
});

// 删除本地资源目录
const localResourcesDir = 'public/local-resources';
if (fs.existsSync(localResourcesDir)) {
  fs.rmSync(localResourcesDir, { recursive: true, force: true });
  console.log('✅ 已删除本地资源目录');
}

console.log('\\n🎉 恢复完成！');
`;

fs.writeFileSync('scripts/restore-localized-resources.cjs', restoreScript);
fs.chmodSync('scripts/restore-localized-resources.cjs', '755');

// 9. 显示总结
console.log('\n🎉 本地化完成！\n');

console.log('📊 处理总结:');
console.log('✅ 浏览器下载链接: 替换为本地信息页面');
console.log('✅ 服务器默认URL: 替换为本地地址');
console.log('✅ 模拟数据URL: 替换为本地帮助页面');
console.log('✅ FontAwesome注释: 移除外部链接');
console.log('✅ 本地帮助页面: 创建完整的替代页面');

console.log('\n📁 创建的本地资源:');
console.log('• public/local-resources/browser-info.html - 浏览器信息页面');
console.log('• public/local-resources/search-help.html - 搜索帮助页面');
console.log('• public/local-resources/api-reference.html - API参考页面');

console.log('• public/local-resources/cli-download.html - CLI下载页面');

console.log('\n🔄 如需恢复原始文件:');
console.log('   node scripts/restore-localized-resources.cjs');

console.log('\n✅ 现在您的项目已完全本地化，可以在内网环境中正常运行！');
console.log('💡 建议运行 npm run build 重新构建项目'); 