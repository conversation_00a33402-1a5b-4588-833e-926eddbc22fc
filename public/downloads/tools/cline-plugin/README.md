# Cline 插件下载目录

本目录包含 Cline AI 编程助手插件的安装文件。

## 目录结构
```
cline-plugin/
├── latest/          # 最新版本插件文件
├── stable/          # 稳定版本插件文件
├── archive/         # 历史版本存档
└── README.md        # 本说明文件
```

## 使用说明
1. 建议下载 `latest/` 目录中的最新版本
2. 如需稳定性，可选择 `stable/` 目录中的稳定版本
3. 下载 .vsix 文件后，在 VS Code 中手动安装

## 文件命名规范
- 最新版: `cline-[version].vsix`
- 稳定版: `cline-stable-[version].vsix`

## 安装步骤
1. 下载 .vsix 文件
2. 打开 VS Code
3. 按 Ctrl+Shift+P 打开命令面板
4. 输入 "Extensions: Install from VSIX"
5. 选择下载的文件进行安装
6. 重启 VS Code

## 更新说明
- 插件会频繁更新以支持新功能
- 建议定期检查并更新到最新版本
- 更新时请先卸载旧版本

---
*最后更新: 2025-01* 