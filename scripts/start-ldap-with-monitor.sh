#!/bin/bash

# LDAP服务启动脚本（带监控）
# 启动LDAP认证服务并同时启动监控脚本

set -e

echo "🚀 启动LDAP认证服务和监控系统..."

# 检查PM2是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2未安装，请先安装PM2: npm install -g pm2"
    exit 1
fi

# 设置环境变量
export NODE_ENV=${NODE_ENV:-development}
export LDAP_AUTH_PORT=${LDAP_AUTH_PORT:-3002}

# 确保日志目录存在
mkdir -p logs
mkdir -p /var/log/ynnx-ai 2>/dev/null || mkdir -p logs/ynnx-ai

# 启动LDAP认证服务
echo "📡 启动LDAP认证服务..."
pm2 start ecosystem.config.js --only ynnx-ldap-auth

# 等待服务启动
echo "⏳ 等待LDAP服务启动..."
sleep 5

# 检查服务是否正常启动
if pm2 list | grep -q "ynnx-ldap-auth.*online"; then
    echo "✅ LDAP认证服务启动成功"
else
    echo "❌ LDAP认证服务启动失败"
    pm2 logs ynnx-ldap-auth --lines 20
    exit 1
fi

# 启动监控脚本
echo "🔍 启动LDAP服务监控..."
pm2 start scripts/monitor-ldap-service.js --name ynnx-ldap-monitor \
    --log-file logs/ldap-monitor.log \
    --error-file logs/ldap-monitor-error.log \
    --restart-delay 3000 \
    --max-restarts 10

# 显示服务状态
echo ""
echo "📊 当前PM2服务状态:"
pm2 list

echo ""
echo "✅ LDAP服务和监控系统启动完成!"
echo ""
echo "📋 有用的命令:"
echo "  查看LDAP服务日志:   pm2 logs ynnx-ldap-auth"
echo "  查看监控日志:       pm2 logs ynnx-ldap-monitor"
echo "  停止所有服务:       pm2 stop all"
echo "  重启LDAP服务:       pm2 restart ynnx-ldap-auth"
echo "  服务健康检查:       curl http://localhost:3002/health"
echo "" 