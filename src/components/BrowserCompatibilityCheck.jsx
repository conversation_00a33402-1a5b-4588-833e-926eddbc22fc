import React, { useState, useEffect, useCallback } from 'react';
import { checkBrowserCompatibility } from '../utils/browserCompatibility';

const BrowserCompatibilityCheck = () => {
  const [compatibility, setCompatibility] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const result = checkBrowserCompatibility();
    setCompatibility(result);
    
    // 如果有兼容性问题，显示通知
    if (!result.isCompatible) {
      setIsVisible(true);
    }
  }, []);

  const toggleDetails = useCallback(() => {
    setShowDetails(prev => !prev);
  }, []);

  const handleDismiss = useCallback(() => {
    setIsVisible(false);
  }, []);

  if (!compatibility || !isVisible) {
    return null;
  }

  return (
    <>
      {!compatibility.isCompatible && (
        <div className="fixed top-4 right-4 z-50 max-w-md animate-fade-in-up">
          <div className="bg-yellow-500/90 backdrop-blur-sm text-black p-4 rounded-lg shadow-lg border border-yellow-400">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <i className="fas fa-exclamation-triangle text-lg"></i>
                <span className="font-semibold">浏览器兼容性提醒</span>
              </div>
              <button
                onClick={handleDismiss}
                className="text-black/60 hover:text-black text-xl leading-none"
                aria-label="关闭"
              >
                ×
              </button>
            </div>
            
            <p className="text-sm mb-3">
              检测到您的浏览器可能不完全支持本平台的所有功能。
            </p>

            <div className="flex items-center gap-2 mb-3">
              <button
                onClick={toggleDetails}
                className="text-sm bg-white/20 px-2 py-1 rounded hover:bg-white/30 transition-colors"
              >
                {showDetails ? '隐藏详情' : '查看详情'}
              </button>
            </div>

            {showDetails && (
              <div className="bg-white/10 rounded p-3 mb-3 text-xs">
                <div className="grid grid-cols-1 gap-2">
                  <div>
                    <strong>浏览器:</strong> {compatibility.browser.name} {compatibility.browser.fullVersion}
                  </div>
                  <div>
                    <strong>平台:</strong> {compatibility.browser.platform}
                  </div>
                  <div>
                    <strong>引擎:</strong> {compatibility.browser.engine}
                  </div>
                  
                  {compatibility.issues && (
                    <div className="mt-2">
                      <strong>兼容性问题:</strong>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        {compatibility.issues.versionTooOld && (
                          <li className="text-red-800">
                            <i className="fas fa-times-circle mr-1"></i>浏览器版本过旧
                          </li>
                        )}
                        {compatibility.issues.unsupportedFeatures?.length > 0 && (
                          <li className="text-red-800">
                            <i className="fas fa-times-circle mr-1"></i>缺少关键特性: {compatibility.issues.unsupportedFeatures.join(', ')}
                          </li>
                        )}
                        {compatibility.issues.missingRecommendedFeatures?.length > 0 && (
                          <li className="text-yellow-800">
                            <i className="fas fa-exclamation-triangle mr-1"></i>缺少推荐特性: {compatibility.issues.missingRecommendedFeatures.join(', ')}
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                  <div className="mt-2">
                    <strong>关键特性支持:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {[
                        { key: 'es6Support', name: 'ES6 语法' },
                        { key: 'fetchAPI', name: 'Fetch API' },
                        { key: 'promiseSupport', name: 'Promise' },
                        { key: 'localStorage', name: '本地存储' },
                        { key: 'cssFlexbox', name: 'CSS Flexbox' },
                        { key: 'cssGrid', name: 'CSS Grid' },
                        { key: 'serviceWorker', name: 'Service Worker' }
                      ].map(({ key, name }) => (
                        <li key={key} className={compatibility.features[key] ? 'text-green-800' : 'text-red-800'}>
                          <i className={`${compatibility.features[key] ? 'fas fa-check-circle' : 'fas fa-times-circle'} mr-1`}></i>{name}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="text-xs">
              <p className="mb-2">建议使用现代浏览器以获得最佳体验：</p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-white/20 px-2 py-1 rounded">Chrome 60+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Firefox 60+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Safari 12+</span>
                <span className="bg-white/20 px-2 py-1 rounded">Edge 79+</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BrowserCompatibilityCheck;