// Service Worker - 缓存优化
const CACHE_NAME = 'ynnx-ai-platform-v1.2';
const STATIC_CACHE = 'ynnx-static-v1.2';
const API_CACHE = 'ynnx-api-v1.2';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.svg'
];

// 需要缓存的API端点（仅GET请求）
const API_ENDPOINTS = [
  '/api/litellm/models',
  '/api/metrics',
  '/api/ldap/test'
];

// 安装事件 - 预缓存关键资源
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      // 强制激活新版本
      self.skipWaiting()
    ])
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  event.waitUntil(
    Promise.all([
      // 清理旧版本缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((cacheName) => {
              return cacheName !== CACHE_NAME && 
                     cacheName !== STATIC_CACHE && 
                     cacheName !== API_CACHE;
            })
            .map((cacheName) => caches.delete(cacheName))
        );
      }),
      // 立即控制所有客户端
      self.clients.claim()
    ])
  );
});

// 请求拦截 - 实现缓存策略
self.addEventListener('fetch', (event) => {
  const { request } = event;
  
  // 跳过非HTTP请求
  if (!request.url.startsWith('http')) {
    return;
  }

  // ⚠️ 跳过POST请求 - Cache API不支持缓存POST请求
  if (request.method !== 'GET') {
    // 对于POST请求，直接转发到网络，不进行缓存，但需要错误处理
    event.respondWith(
      fetch(request).catch(error => {
        console.warn('POST请求失败:', error);
        // 返回合适的错误响应
        return new Response(
          JSON.stringify({
            error: '网络连接失败',
            message: '无法连接到服务器，请检查网络连接',
            timestamp: new Date().toISOString()
          }),
          {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      })
    );
    return;
  }

  // 静态资源 - 缓存优先策略
  if (isStaticAsset(request)) {
    event.respondWith(cacheFirst(request, STATIC_CACHE));
    return;
  }
  
  // API请求 - 网络优先，降级到缓存（仅GET请求）
  if (isAPIRequest(request)) {
    event.respondWith(networkFirst(request, API_CACHE));
    return;
  }
  
  // HTML请求 - 网络优先
  if (request.destination === 'document') {
    event.respondWith(networkFirst(request, STATIC_CACHE));
    return;
  }
  
  // 其他资源 - 网络优先
  event.respondWith(networkFirst(request, CACHE_NAME));
});

// 判断是否为静态资源
function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|svg|ico|woff2?|ttf)$/);
}

// 判断是否为API请求（仅GET请求）
function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/') || 
         API_ENDPOINTS.some(endpoint => url.pathname.includes(endpoint));
}

// 缓存优先策略
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 后台更新缓存（非阻塞）
      updateCache(request, cache);
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.warn('缓存优先策略失败:', error);
    return new Response('离线模式 - 资源不可用', { 
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// 网络优先策略
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    // 只缓存成功的GET请求
    if (networkResponse.ok && request.method === 'GET') {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.warn('网络请求失败:', error);
    
    // 仅对GET请求尝试从缓存获取
    if (request.method === 'GET') {
      const cache = await caches.open(cacheName);
      const cachedResponse = await cache.match(request);
      
      if (cachedResponse) {
        // 添加离线标识头
        const response = cachedResponse.clone();
        response.headers.set('X-Served-From', 'cache');
        return response;
      }
    }
    
    // 根据请求类型返回不同的错误响应
    if (request.url.includes('/api/')) {
      return new Response(
        JSON.stringify({
          error: '服务不可用',
          message: '无法连接到API服务器',
          offline: true,
          timestamp: new Date().toISOString()
        }),
        {
          status: 503,
          statusText: 'Service Unavailable',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    
    return new Response('网络不可用，请检查连接', { 
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// 后台更新缓存
async function updateCache(request, cache) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse);
    }
  } catch {
    // 静默失败，不影响用户体验
  }
}

// 监听消息 - 手动更新缓存
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'UPDATE_CACHE') {
    // 强制更新所有缓存
    caches.keys().then((cacheNames) => {
      cacheNames.forEach(cacheName => {
        caches.delete(cacheName);
      });
    });
  }
}); 