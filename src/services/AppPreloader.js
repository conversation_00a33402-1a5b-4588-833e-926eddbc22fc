/**
 * 统一的应用预加载管理器
 * 在应用启动早期统一管理所有服务的初始化，确保用户看到页面时所有功能都已就绪
 */

import enhancedDocumentService from './documentService';
import pageContentCollector from './pageContentCollector';

class AppPreloader {
  constructor() {
    this.preloadStatus = {
      documentsLoaded: false,
      pageContentCollected: false,
      startTime: null,
      endTime: null,
      totalProgress: 0,
      errors: [],
      isComplete: false,
      isChatReady: false, // 新增：聊天助手就绪状态
      minimumDataLoaded: false // 新增：最小数据加载完成状态
    };
    
    this.preloadPromise = null;
    this.progressCallbacks = new Set();
    this.completionCallbacks = new Set();
    this.chatReadyCallbacks = new Set(); // 新增：聊天就绪回调
    
    // 超时设置
    this.timeout = 15000; // 15秒超时
    this.timeoutTimer = null;

    // 添加数据缓存
    this.cachedData = {
      documents: null,
      pageContent: null,
      documentStats: null
    };

    // 新增：最小数据要求
    this.minimumRequirements = {
      minDocuments: 1, // 至少需要1个文档
      minPageSections: 1, // 至少需要1个页面区域
      requireBasicContent: true // 需要基本内容
    };
  }

  /**
   * 开始预加载过程
   */
  async startPreload() {
    if (this.preloadPromise) {
      return this.preloadPromise;
    }

    this.preloadStatus.startTime = Date.now();
    console.log('[AppPreloader] 开始应用预加载...');

    this.preloadPromise = this.executePreload();
    return this.preloadPromise;
  }

  /**
   * 执行预加载
   */
  async executePreload() {
    try {
      // 设置超时
      this.timeoutTimer = setTimeout(() => {
        console.warn('[AppPreloader] 预加载超时，使用部分加载的数据');
        this.handleTimeout();
      }, this.timeout);

      // 并行执行预加载任务
      const tasks = [
        this.preloadDocuments(),
        this.preloadPageContent()
      ];

      await Promise.allSettled(tasks);

      // 清除超时定时器
      if (this.timeoutTimer) {
        clearTimeout(this.timeoutTimer);
        this.timeoutTimer = null;
      }

      this.preloadStatus.endTime = Date.now();
      this.preloadStatus.isComplete = true;
      this.preloadStatus.totalProgress = 100;

      const duration = this.preloadStatus.endTime - this.preloadStatus.startTime;
      console.log(`[AppPreloader] 预加载完成，耗时: ${duration}ms`);

      // 检查最小数据要求
      this.checkMinimumRequirements();

      // 通知完成回调
      this.completionCallbacks.forEach(callback => {
        try {
          callback(this.preloadStatus);
        } catch (error) {
          console.error('[AppPreloader] 完成回调执行失败:', error);
        }
      });

      return this.preloadStatus;
    } catch (error) {
      console.error('[AppPreloader] 预加载失败:', error);
      this.preloadStatus.errors.push(error.message);
      this.preloadStatus.isComplete = true;
      this.preloadStatus.totalProgress = 100;
      
      // 即使失败也要通知完成
      this.completionCallbacks.forEach(callback => {
        try {
          callback(this.preloadStatus);
        } catch (callbackError) {
          console.error('[AppPreloader] 完成回调执行失败:', callbackError);
        }
      });

      return this.preloadStatus;
    }
  }

  /**
   * 预加载文档库
   */
  async preloadDocuments() {
    try {
      console.log('[AppPreloader] 开始预加载文档库...');
      this.updateProgress(10, '正在加载文档库...');

      // 启动文档服务预加载
      const documentsPromise = enhancedDocumentService.getAllDocumentsContent();
      
      // 监听文档加载进度
      const checkDocumentProgress = () => {
        const stats = enhancedDocumentService.getLoadingStatus();
        if (stats.status === 'loading' && stats.progress) {
          const progress = 10 + (stats.progress.loaded / stats.progress.total) * 40;
          this.updateProgress(Math.min(progress, 50), '正在加载文档库...');
        }
      };

      const progressInterval = setInterval(checkDocumentProgress, 500);

      try {
        const documents = await documentsPromise;
        clearInterval(progressInterval);
        
        // 缓存加载的文档数据
        this.cachedData.documents = documents;
        this.cachedData.documentStats = enhancedDocumentService.getDocumentStats();
        
        this.preloadStatus.documentsLoaded = true;
        this.updateProgress(50, '文档库加载完成');
        console.log('[AppPreloader] 文档库预加载完成');
      } catch (error) {
        clearInterval(progressInterval);
        throw error;
      }
    } catch (error) {
      console.error('[AppPreloader] 文档库预加载失败:', error);
      this.preloadStatus.errors.push(`文档库加载失败: ${error.message}`);
      // 即使失败也要更新进度，允许应用继续运行
      this.updateProgress(50, '文档库加载失败，将使用降级模式');
    }
  }

  /**
   * 预加载页面内容
   */
  async preloadPageContent() {
    try {
      console.log('[AppPreloader] 开始预加载页面内容...');
      this.updateProgress(60, '正在收集页面内容...');

      // 等待DOM完全加载
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve, { once: true });
        });
      }

      // 等待React组件挂载
      await new Promise(resolve => setTimeout(resolve, 100));

      // 启动页面内容收集
      const pageContent = await pageContentCollector.collectPageContent();
      
      // 缓存页面内容
      this.cachedData.pageContent = pageContent;
      
      this.preloadStatus.pageContentCollected = true;
      this.updateProgress(90, '页面内容收集完成');
      console.log('[AppPreloader] 页面内容预加载完成');
    } catch (error) {
      console.error('[AppPreloader] 页面内容预加载失败:', error);
      this.preloadStatus.errors.push(`页面内容收集失败: ${error.message}`);
      // 即使失败也要更新进度
      this.updateProgress(90, '页面内容收集失败，将使用基础模式');
    }
  }

  /**
   * 处理超时情况
   */
  handleTimeout() {
    console.warn('[AppPreloader] 预加载超时，强制完成');
    this.preloadStatus.isComplete = true;
    this.preloadStatus.totalProgress = 100;
    this.preloadStatus.errors.push('预加载超时，部分功能可能不可用');
    
    // 检查是否满足最小要求
    this.checkMinimumRequirements();
    
    // 通知完成回调
    this.completionCallbacks.forEach(callback => {
      try {
        callback(this.preloadStatus);
      } catch (error) {
        console.error('[AppPreloader] 超时完成回调执行失败:', error);
      }
    });
  }

  /**
   * 检查最小数据要求
   */
  checkMinimumRequirements() {
    const { documents, pageContent, documentStats } = this.cachedData;
    
    let meetRequirements = true;
    const failedChecks = [];

    // 检查文档要求
    if (this.minimumRequirements.minDocuments > 0) {
      const totalDocs = documentStats?.totalDocuments || 0;
      if (totalDocs < this.minimumRequirements.minDocuments) {
        meetRequirements = false;
        failedChecks.push(`文档数量不足: ${totalDocs}/${this.minimumRequirements.minDocuments}`);
      }
    }

    // 检查页面内容要求
    if (this.minimumRequirements.minPageSections > 0) {
      const sectionsCount = pageContent?.structure?.sections?.length || 0;
      if (sectionsCount < this.minimumRequirements.minPageSections) {
        meetRequirements = false;
        failedChecks.push(`页面区域不足: ${sectionsCount}/${this.minimumRequirements.minPageSections}`);
      }
    }

    // 检查基本内容要求
    if (this.minimumRequirements.requireBasicContent) {
      const hasBasicContent = (
        documents && Object.keys(documents).length > 0
      ) || (
        pageContent && pageContent.title && pageContent.structure
      );
      
      if (!hasBasicContent) {
        meetRequirements = false;
        failedChecks.push('缺少基本内容数据');
      }
    }

    this.preloadStatus.minimumDataLoaded = meetRequirements;
    
    if (meetRequirements) {
      this.preloadStatus.isChatReady = true;
      console.log('[AppPreloader] 最小数据要求已满足，聊天助手可用');
      
      // 通知聊天就绪回调
      this.chatReadyCallbacks.forEach(callback => {
        try {
          callback(this.preloadStatus);
        } catch (error) {
          console.error('[AppPreloader] 聊天就绪回调执行失败:', error);
        }
      });
    } else {
      console.warn('[AppPreloader] 最小数据要求未满足:', failedChecks.join(', '));
      this.preloadStatus.errors.push(`最小数据要求未满足: ${failedChecks.join(', ')}`);
    }
  }

  /**
   * 注册聊天就绪回调
   */
  onChatReady(callback) {
    this.chatReadyCallbacks.add(callback);
    
    // 如果已经就绪，立即执行回调
    if (this.preloadStatus.isChatReady) {
      try {
        callback(this.preloadStatus);
      } catch (error) {
        console.error('[AppPreloader] 聊天就绪回调执行失败:', error);
      }
    }
  }

  /**
   * 检查聊天助手是否就绪
   */
  isChatReady() {
    return this.preloadStatus.isChatReady;
  }

  /**
   * 更新进度
   */
  updateProgress(progress, message) {
    this.preloadStatus.totalProgress = Math.min(progress, 100);
    
    // 通知进度回调
    this.progressCallbacks.forEach(callback => {
      try {
        callback(this.preloadStatus.totalProgress, message);
      } catch (error) {
        console.error('[AppPreloader] 进度回调执行失败:', error);
      }
    });
  }

  /**
   * 注册进度回调
   */
  onProgress(callback) {
    this.progressCallbacks.add(callback);
    return () => this.progressCallbacks.delete(callback);
  }

  /**
   * 注册完成回调
   */
  onComplete(callback) {
    this.completionCallbacks.add(callback);
    return () => this.completionCallbacks.delete(callback);
  }

  /**
   * 获取预加载状态
   */
  getStatus() {
    return { ...this.preloadStatus };
  }

  /**
   * 检查是否已完成
   */
  isComplete() {
    return this.preloadStatus.isComplete;
  }

  /**
   * 获取预加载的数据
   */
  getPreloadedData() {
    return {
      documents: this.preloadStatus.documentsLoaded ? this.cachedData.documents : null,
      pageContent: this.preloadStatus.pageContentCollected ? this.cachedData.pageContent : null,
      documentStats: this.preloadStatus.documentsLoaded ? this.cachedData.documentStats : null
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
    this.progressCallbacks.clear();
    this.completionCallbacks.clear();
    this.chatReadyCallbacks.clear();
  }
}

// 创建单例实例
const appPreloader = new AppPreloader();

export default appPreloader;
