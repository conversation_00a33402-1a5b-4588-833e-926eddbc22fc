# LobeChat 集成文档

## 概述

本文档描述了如何将 LobeChat 智能对话系统集成到 YNNX AI 平台中，实现与现有 LDAP 认证系统的无缝集成和用户会话隔离。

## 集成架构

### 1. 服务架构
```
YNNX Platform (nginx) → LobeChat (http://***********:3210)
                     ↓
                 /chat/ 代理路径
```

### 2. 认证流程
```
用户登录 LDAP → 平台生成认证令牌 → nginx 代理传递认证头 → LobeChat 识别用户
```

## 部署配置

### 1. LobeChat 外部服务配置

LobeChat 作为独立服务运行在 `http://***********:3210`，需要配置以下环境变量：

```bash
# LobeChat 基础配置
NEXT_PUBLIC_SERVICE_MODE=server
NEXT_PUBLIC_ENABLE_NEXT_AUTH=1

# 认证配置
NEXT_AUTH_SECRET=your-generated-secret-key
NEXTAUTH_URL=http://***********:3210/api/auth
NEXT_AUTH_SSO_PROVIDERS=generic-oidc

# 自定义认证提供者配置（用于接收平台认证）
AUTH_GENERIC_OIDC_ID=ynnx-platform
AUTH_GENERIC_OIDC_SECRET=ynnx-chat-token-2025
AUTH_GENERIC_OIDC_ISSUER=http://***********/api/auth

# 数据库配置（如果使用数据库模式）
DATABASE_URL=postgresql://user:password@localhost:5432/lobechat

# 其他配置
APP_URL=http://***********:3210
```

### 2. nginx 代理配置

已在 `nginx/conf.d/default.conf` 中添加：

```nginx
location /chat/ {
    rewrite ^/chat/(.*)$ /$1 break;
    proxy_pass http://***********:3210;
    
    # 认证头部传递
    proxy_set_header X-Auth-User $http_x_auth_user;
    proxy_set_header X-Auth-Email $http_x_auth_email;
    proxy_set_header X-Auth-Name $http_x_auth_name;
    proxy_set_header X-Platform-Auth "ynnx-platform";
    proxy_set_header X-Platform-Token "ynnx-chat-token-2025";
    
    # WebSocket 支持
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;
    
    # 其他必要头部...
}
```

### 3. 前端集成

#### 组件结构
- `src/components/ChatSection.jsx` - 主要聊天组件
- `src/services/chatAuthService.js` - 认证服务

#### 认证流程
1. 用户登录平台后，`chatAuthService` 生成认证令牌
2. 令牌通过 nginx 头部传递给 LobeChat
3. LobeChat 验证令牌并创建用户会话
4. 实现用户会话隔离

## 安全特性

### 1. 用户会话隔离
- 每个用户获得独立的认证令牌
- 会话数据基于用户身份隔离
- 防止用户间数据泄露

### 2. 认证安全
- 令牌包含签名验证
- 24小时自动过期
- 平台级别的权限控制

### 3. iframe 安全
- 沙箱限制：`allow-same-origin allow-scripts allow-forms allow-modals allow-pointer-lock allow-popups`
- 同源策略保护
- 防止恶意脚本执行

## 使用说明

### 1. 用户访问流程
1. 用户登录 YNNX 平台
2. 导航到"智能对话"部分
3. 系统自动设置认证信息
4. LobeChat 在 iframe 中加载
5. 用户可以开始对话

### 2. 功能特性
- 多轮对话支持
- 代码生成和解释
- 文件上传（如果 LobeChat 支持）
- 多模态交互
- 对话历史保存

### 3. 权限管理
- 基于 LDAP 用户身份
- 可配置访问权限
- 支持用户组权限控制

## 故障排除

### 1. 常见问题

#### LobeChat 无法加载
- 检查 LobeChat 服务是否运行在 `http://***********:3210`
- 验证 nginx 代理配置是否正确
- 查看浏览器控制台错误信息

#### 认证失败
- 检查用户是否已登录平台
- 验证认证令牌是否正确生成
- 查看 nginx 日志中的认证头部信息

#### WebSocket 连接问题
- 确认 nginx 配置包含 WebSocket 升级头
- 检查防火墙设置
- 验证 LobeChat 的 WebSocket 配置

### 2. 调试方法

#### 前端调试
```javascript
// 检查认证状态
console.log(chatAuthService.getAuthStatus());

// 查看认证令牌
console.log(chatAuthService.getChatAuthToken());

// 检查会话信息
console.log(chatAuthService.getChatSession());
```

#### nginx 日志
```bash
# 查看访问日志
tail -f /var/log/nginx/access.log | grep "/chat/"

# 查看错误日志
tail -f /var/log/nginx/error.log
```

## 维护和更新

### 1. 定期维护
- 监控 LobeChat 服务状态
- 检查认证令牌过期情况
- 清理过期会话数据

### 2. 更新流程
1. 更新 LobeChat 服务
2. 测试认证集成
3. 验证用户会话隔离
4. 部署到生产环境

### 3. 监控指标
- LobeChat 服务可用性
- 用户认证成功率
- 会话创建和过期情况
- iframe 加载性能

## 扩展功能

### 1. 可能的增强
- 支持更多认证提供者
- 添加用户偏好设置
- 实现对话数据备份
- 集成更多 AI 模型

### 2. 自定义配置
- 用户界面主题
- 对话模型选择
- 功能权限控制
- 数据保留策略

## 注意事项

1. **外部依赖**: LobeChat 作为外部服务，需要确保其稳定运行
2. **数据隐私**: 用户对话数据的处理需要符合隐私政策
3. **性能考虑**: iframe 嵌入可能影响页面性能
4. **兼容性**: 需要测试不同浏览器的兼容性
5. **安全更新**: 定期更新 LobeChat 以获得安全补丁
