/* 优化版本的字体配置 - 只加载必要的图标 */

/* 兼容旧浏览器，添加多种字体格式 */
@font-face {
  font-family: 'fa-solid-900';
  src: url('../webfonts/fa-solid-900.woff2') format('woff2'),
       url('../webfonts/fa-solid-900.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap; /* 优化字体加载 */
}

@font-face {
  font-family: 'fa-regular-400';
  src: url('../webfonts/fa-regular-400.woff2') format('woff2'),
       url('../webfonts/fa-regular-400.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'fa-brands-400';
  src: url('../webfonts/fa-brands-400.woff2') format('woff2'),
       url('../webfonts/fa-brands-400.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* 基础样式 */
.fa, .fas, .far, .fab {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* 兼容不同的FontAwesome版本 */
.fas, .fa-solid {
  font-family: 'fa-solid-900';
  font-weight: 900;
}

.far, .fa-regular {
  font-family: 'fa-regular-400';
  font-weight: 400;
}

.fab, .fa-brands {
  font-family: 'fa-brands-400';
  font-weight: 400;
}

/* 旧版本兼容性 */
.fas::before, .fa-solid::before {
  font-family: 'fa-solid-900';
  font-weight: 900;
}

.far::before, .fa-regular::before {
  font-family: 'fa-regular-400';
  font-weight: 400;
}

.fab::before, .fa-brands::before {
  font-family: 'fa-brands-400';
  font-weight: 400;
}

/* emoji兼容性替换图标 - 包含所有emoji对应的图标 */
.fa-rocket::before { content: "\f135"; }
.fa-keyboard::before { content: "\f11c"; }
.fa-search::before { content: "\f002"; }
.fa-cog::before { content: "\f013"; }
.fa-wrench::before { content: "\f0ad"; }
.fa-clipboard::before { content: "\f328"; }
.fa-lightbulb::before { content: "\f0eb"; }
.fa-star::before { content: "\f005"; }
.fa-bullseye::before { content: "\f140"; }
.fa-key::before { content: "\f084"; }
.fa-hand::before { content: "\f256"; }
.fa-comments::before { content: "\f086"; }
.fa-book::before { content: "\f02d"; }
.fa-home::before { content: "\f015"; }
.fa-chart-bar::before { content: "\f080"; }
.fa-user::before { content: "\f007"; }
.fa-desktop::before { content: "\f108"; }
.fa-mobile-alt::before { content: "\f3cd"; }
.fa-globe::before { content: "\f0ac"; }
.fa-lock::before { content: "\f023"; }
.fa-file::before { content: "\f15b"; }
.fa-edit::before { content: "\f044"; }
.fa-palette::before { content: "\f53f"; }
.fa-truck::before { content: "\f0d1"; }
.fa-clock::before { content: "\f017"; }
.fa-phone::before { content: "\f095"; }
.fa-envelope::before { content: "\f0e0"; }
.fa-link::before { content: "\f0c1"; }
.fa-box::before { content: "\f466"; }
.fa-gamepad::before { content: "\f11b"; }
.fa-music::before { content: "\f001"; }
.fa-camera::before { content: "\f030"; }
.fa-video::before { content: "\f03d"; }
.fa-heart::before { content: "\f004"; }
.fa-thumbs-up::before { content: "\f164"; }
.fa-thumbs-down::before { content: "\f165"; }
.fa-bolt::before { content: "\f0e7"; }
.fa-fire::before { content: "\f06d"; }
.fa-snowflake::before { content: "\f2dc"; }
.fa-sun::before { content: "\f185"; }
.fa-moon::before { content: "\f186"; }
.fa-cloud::before { content: "\f0c2"; }
.fa-rainbow::before { content: "\f75b"; }
.fa-laptop-code::before { content: "\f5fc"; }
.fa-graduation-cap::before { content: "\f19d"; }
.fa-robot::before { content: "\f544"; }
.fa-brain::before { content: "\f5dc"; }
.fa-check-circle::before { content: "\f058"; }
.fa-times-circle::before { content: "\f057"; }
.fa-bell::before { content: "\f0f3"; }
.fa-trophy::before { content: "\f091"; }
.fa-gift::before { content: "\f06b"; }
.fa-magic::before { content: "\f0d0"; }
.fa-theater-masks::before { content: "\f630"; }
.fa-party-horn::before { content: "\f0d0"; }
.fa-globe-americas::before { content: "\f57d"; }
.fa-tools::before { content: "\f7d9"; }
.fa-sync-alt::before { content: "\f2f1"; }
.fa-shield-alt::before { content: "\f3ed"; }
.fa-user-cog::before { content: "\f4fe"; }
.fa-fist-raised::before { content: "\f6de"; }
.fa-medal::before { content: "\f5a2"; }
.fa-ribbon::before { content: "\f4d6"; }
.fa-tv::before { content: "\f26c"; }
.fa-radio::before { content: "\f8d7"; }
.fa-balance-scale::before { content: "\f24e"; }
.fa-building::before { content: "\f1ad"; }
.fa-running::before { content: "\f70c"; }
.fa-walking::before { content: "\f554"; }
.fa-dumbbell::before { content: "\f44b"; }
.fa-volume-up::before { content: "\f028"; }
.fa-volume-mute::before { content: "\f6a9"; }

/* 常用功能图标 */
.fa-download::before { content: "\f019"; }
.fa-info-circle::before { content: "\f05a"; }
.fa-external-link-alt::before { content: "\f35d"; }
.fa-file-alt::before { content: "\f15c"; }
.fa-code::before { content: "\f121"; }

/* 品牌图标 */
.fa-github::before { content: "\f09b"; }
.fa-docker::before { content: "\f395"; }
.fa-python::before { content: "\f3e2"; }
.fa-node-js::before { content: "\f3d3"; }

/* 响应式字体大小 */
@media (max-width: 768px) {
  .fa {
    font-size: 0.9em;
  }
}

/* 低性能模式 - 禁用字体阴影和特效 */
.low-performance .fa {
  text-shadow: none;
  filter: none;
} 