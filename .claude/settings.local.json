{"permissions": {"allow": ["WebFetch(domain:docs.copilotkit.ai)", "Bash(rg:*)", "Bash(npm run lint)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "Bash(docker logs:*)", "Bash(docker port:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(node:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:192.168.1.3)", "Bash(npm uninstall:*)", "Bash(nginx:*)", "<PERSON><PERSON>(openssl req:*)", "<PERSON><PERSON>(docker cp:*)", "Bash(sudo rm:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(env)", "Bash(VITE_LITELLM_API_BASE=https://192.168.1.3:4000 npm run build)", "Bash(VITE_LITELLM_API_BASE=/api/litellm npm run build)", "Bash(VITE_LDAP_API_URL=/api/ldap VITE_LITELLM_API_BASE=/api/litellm npm run build)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_install", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_network_requests", "Bash(cp:*)", "<PERSON>sh(sudo cp:*)", "mcp__playwright__browser_wait_for"], "deny": []}}