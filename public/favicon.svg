<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 强发光滤镜 -->
    <filter id="strongGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 轻发光滤镜 -->
    <filter id="lightGlow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="0.8" result="softBlur"/>
      <feMerge> 
        <feMergeNode in="softBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 主渐变 - 青蓝色 -->
    <radialGradient id="mainGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#67e8f9;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </radialGradient>
    
    <!-- 次要渐变 - 紫色 -->
    <radialGradient id="accentGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#a78bfa;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
    </radialGradient>
    
    <!-- 高亮渐变 - 青绿色 -->
    <radialGradient id="highlightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#5eead4;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- 深色背景 -->
  <rect width="32" height="32" fill="#000000" rx="6"/>
  
  <!-- 中央核心 - 大型发光像素（代表AI核心） -->
  <rect x="12" y="12" width="8" height="8" fill="url(#mainGradient)" filter="url(#strongGlow)" rx="1"/>
  
  <!-- 围绕核心的像素点阵（象征数据流和连接） -->
  <!-- 上方像素群 -->
  <rect x="8" y="4" width="3" height="3" fill="url(#accentGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="13" y="3" width="3" height="3" fill="url(#highlightGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="19" y="5" width="3" height="3" fill="url(#mainGradient)" filter="url(#lightGlow)" rx="0.5"/>
  
  <!-- 左侧像素群 -->
  <rect x="3" y="9" width="3" height="3" fill="url(#highlightGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="5" y="16" width="3" height="3" fill="url(#accentGradient)" filter="url(#lightGlow)" rx="0.5"/>
  
  <!-- 右侧像素群 -->
  <rect x="26" y="8" width="3" height="3" fill="url(#accentGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="24" y="17" width="3" height="3" fill="url(#highlightGradient)" filter="url(#lightGlow)" rx="0.5"/>
  
  <!-- 下方像素群 -->
  <rect x="7" y="24" width="3" height="3" fill="url(#mainGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="14" y="26" width="3" height="3" fill="url(#accentGradient)" filter="url(#lightGlow)" rx="0.5"/>
  <rect x="21" y="23" width="3" height="3" fill="url(#highlightGradient)" filter="url(#lightGlow)" rx="0.5"/>
  
  <!-- 连接线条（数据流效果） -->
  <rect x="14" y="7" width="1" height="4" fill="url(#highlightGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="17" y="7" width="1" height="4" fill="url(#accentGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="21" y="14" width="4" height="1" fill="url(#mainGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="21" y="17" width="4" height="1" fill="url(#highlightGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="14" y="21" width="1" height="4" fill="url(#accentGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="17" y="21" width="1" height="4" fill="url(#mainGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="7" y="14" width="4" height="1" fill="url(#highlightGradient)" opacity="0.6" filter="url(#lightGlow)"/>
  <rect x="7" y="17" width="4" height="1" fill="url(#accentGradient)" opacity="0.6" filter="url(#lightGlow)"/>
</svg> 