#!/usr/bin/env node

// YNNX AI Platform - LDAP认证服务启动脚本
// 启动LDAP认证API服务器

/* eslint-env node */
/* global process */

import dotenv from 'dotenv';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// 获取当前脚本目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 加载环境变量（支持不同环境文件）
const nodeEnv = process.env.NODE_ENV || 'development';

// 按优先级加载环境文件
const envFiles = [
  `.env.${nodeEnv}.local`,
  `.env.${nodeEnv}`,
  '.env.local',
  '.env'
];

envFiles.forEach(file => {
  const envPath = join(__dirname, '..', file);
  dotenv.config({ path: envPath });
});

console.log(`🔧 加载环境: ${nodeEnv}`);

console.log('🚀 启动LDAP认证服务...');

// 获取LDAP服务器文件路径
const ldapServerPath = join(__dirname, '../src/server/ldapAuthServer.js');

// 使用子进程启动LDAP服务器
const ldapProcess = spawn('node', [ldapServerPath], {
  stdio: 'inherit',
  env: process.env
});

ldapProcess.on('error', (error) => {
  console.error('❌ LDAP服务启动失败:', error.message);
  process.exit(1);
});

ldapProcess.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ LDAP服务异常退出，退出码: ${code}`);
    process.exit(code);
  }
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在停止LDAP服务...');
  ldapProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在停止LDAP服务...');
  ldapProcess.kill('SIGTERM');
});

console.log('LDAP认证服务启动脚本执行完成'); 