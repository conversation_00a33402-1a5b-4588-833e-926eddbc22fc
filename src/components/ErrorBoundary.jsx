import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 在生产环境中，可以将错误发送到错误报告服务
    if (import.meta.env.PROD) {
      // TODO: 发送错误到监控服务
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-black flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-gray-900 rounded-lg p-6 text-center">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto bg-red-600 rounded-full flex items-center justify-center mb-4">
                <span className="text-white text-2xl">⚠️</span>
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">出现了一些问题</h2>
              <p className="text-gray-400 text-sm mb-4">
                抱歉，应用遇到了意外错误。请刷新页面重试。
              </p>
            </div>
            
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-cyan-600 hover:bg-cyan-700 text-white py-2 px-4 rounded transition-colors"
              >
                刷新页面
              </button>
              
              <button
                onClick={() => this.setState({ hasError: false, error: null })}
                className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
              >
                重试
              </button>
            </div>
            
            {import.meta.env.DEV && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-gray-300 text-sm">查看错误详情</summary>
                <pre className="text-xs text-red-400 mt-2 p-2 bg-gray-800 rounded overflow-auto">
                  {this.state.error.toString()}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 