# JetBrains AI Assistant 下载目录

本目录包含 JetBrains AI Assistant 插件的安装文件。

## 目录结构
```
jetbrains-ai-assistant/
├── latest/          # 最新版本插件文件
├── stable/          # 稳定版本插件文件
├── ides/            # 分IDE版本的插件文件
│   ├── idea/        # IntelliJ IDEA 专用版本
│   ├── pycharm/     # PyCharm 专用版本
│   └── webstorm/    # WebStorm 专用版本
└── README.md        # 本说明文件
```

## 使用说明
1. 建议下载 `latest/` 目录中的最新版本（通用版本）
2. 如需特定IDE优化，可选择 `ides/` 对应子目录
3. 稳定性要求高可选择 `stable/` 目录版本
4. 下载 .jar 或 .zip 文件后，在IDE中手动安装

## 文件命名规范
- 通用版: `jetbrains-ai-assistant-[version].jar`
- IDEA版: `idea-ai-assistant-[version].jar`
- PyCharm版: `pycharm-ai-assistant-[version].jar`
- WebStorm版: `webstorm-ai-assistant-[version].jar`

## 支持的IDE
- ✅ IntelliJ IDEA (Ultimate/Community)
- ✅ PyCharm (Professional/Community)
- ✅ WebStorm
- ✅ Android Studio
- ✅ CLion、GoLand、Rider等其他JetBrains IDE

## 系统要求
- IDE版本：2023.2 或更高
- Java版本：JDK 11 或更高
- 内存：建议8GB以上

## 安装步骤
1. 下载对应的插件文件
2. 打开JetBrains IDE
3. 进入 File → Settings → Plugins
4. 点击齿轮图标 → Install Plugin from Disk
5. 选择下载的插件文件
6. 重启IDE使插件生效

## 更新说明
- 插件会跟随IDE版本更新
- 建议与IDE同步更新以获得最佳兼容性
- 支持离线安装，无需网络连接

---
*最后更新: 2025-01* 