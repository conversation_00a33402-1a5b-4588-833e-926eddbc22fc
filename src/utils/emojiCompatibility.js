// emoji兼容性检测和处理工具
class EmojiCompatibility {
  constructor() {
    this.emojiToIcon = new Map([
      ['🚀', 'fas fa-rocket'],
      ['⌨️', 'fas fa-keyboard'],
      ['🔍', 'fas fa-search'],
      ['⚙️', 'fas fa-cog'],
      ['🔧', 'fas fa-wrench'],
      ['📋', 'fas fa-clipboard'],
      ['💡', 'fas fa-lightbulb'],
      ['✨', 'fas fa-star'],
      ['🎯', 'fas fa-bullseye'],
      ['🔑', 'fas fa-key'],
      ['👋', 'fas fa-hand'],
      ['💬', 'fas fa-comments'],
      ['📖', 'fas fa-book'],
      ['🏠', 'fas fa-home'],
      ['📊', 'fas fa-chart-bar'],
      ['👤', 'fas fa-user'],
      ['🖥️', 'fas fa-desktop'],
      ['📱', 'fas fa-mobile-alt'],
      ['🌐', 'fas fa-globe'],
      ['🔒', 'fas fa-lock'],
      ['📄', 'fas fa-file'],
      ['📝', 'fas fa-edit'],
      ['🎨', 'fas fa-palette'],
      ['🚚', 'fas fa-truck'],
      ['⏰', 'fas fa-clock'],
      ['📞', 'fas fa-phone'],
      ['📧', 'fas fa-envelope'],
      ['🔗', 'fas fa-link'],
      ['📦', 'fas fa-box'],
      ['🎮', 'fas fa-gamepad'],
      ['🎵', 'fas fa-music'],
      ['📸', 'fas fa-camera'],
      ['🎥', 'fas fa-video'],
      ['🌟', 'fas fa-star'],
      ['❤️', 'fas fa-heart'],
      ['👍', 'fas fa-thumbs-up'],
      ['👎', 'fas fa-thumbs-down'],
      ['⚡', 'fas fa-bolt'],
      ['🔥', 'fas fa-fire'],
      ['❄️', 'fas fa-snowflake'],
      ['☀️', 'fas fa-sun'],
      ['🌙', 'fas fa-moon'],
      ['☁️', 'fas fa-cloud'],
      ['🌈', 'fas fa-rainbow'],
      ['💻', 'fas fa-laptop-code'],
      ['🎓', 'fas fa-graduation-cap'],
      ['🤖', 'fas fa-robot'],
      ['🧠', 'fas fa-brain'],
      ['✅', 'fas fa-check-circle'],
      ['❌', 'fas fa-times-circle'],
      ['🔔', 'fas fa-bell'],
      ['🏆', 'fas fa-trophy'],
      ['🎁', 'fas fa-gift'],
      ['🔮', 'fas fa-magic'],
      ['🎪', 'fas fa-magic'],
      ['🎭', 'fas fa-theater-masks'],
      ['🎉', 'fas fa-party-horn'],
      ['🌍', 'fas fa-globe-americas'],
      ['🛠️', 'fas fa-tools'],
      ['🔄', 'fas fa-sync-alt'],
      ['🔐', 'fas fa-shield-alt'],
      ['⭐', 'fas fa-star'],
      ['👨‍💻', 'fas fa-user-cog'],
      ['👩‍💻', 'fas fa-user-cog'],
      ['💪', 'fas fa-fist-raised'],
      ['🎖️', 'fas fa-medal'],
      ['🏅', 'fas fa-medal'],
      ['🥇', 'fas fa-medal'],
      ['🥈', 'fas fa-medal'],
      ['🥉', 'fas fa-medal'],
      ['🎗️', 'fas fa-ribbon'],
      ['🎀', 'fas fa-gift'],
      ['🎊', 'fas fa-magic'],
      ['🌟', 'fas fa-star'],
      ['📺', 'fas fa-tv'],
      ['📻', 'fas fa-radio'],
      ['⚖️', 'fas fa-balance-scale'],
      ['🏢', 'fas fa-building'],
      ['🏃', 'fas fa-running'],
      ['🚶', 'fas fa-walking'],
      ['🏋️', 'fas fa-dumbbell'],
      ['🔊', 'fas fa-volume-up'],
      ['🔇', 'fas fa-volume-mute']
    ]);
    
    this.isEmojiSupported = null;
    this.isIntranet = null;
    this.debounceTimer = null;
    this.disabled = false; // 重新启用，使用React安全模式
    this.reactSafeMode = true; // 启用React安全模式
  }

  // 安全移除DOM元素的辅助函数
  safeRemoveElement(element) {
    try {
      if (element && element.parentNode && element.parentNode.contains(element)) {
        element.parentNode.removeChild(element);
        return true;
      }
    } catch (error) {
      console.warn('[EMOJI] 安全移除元素失败:', error.message);
    }
    return false;
  }

  // 安全的DOM操作包装器
  safeDOMOperation(operation, errorMessage = 'DOM操作失败') {
    try {
      return operation();
    } catch (error) {
      console.warn(`[EMOJI] ${errorMessage}:`, error.message);
      return null;
    }
  }

  // 防抖的emoji替换方法
  debouncedReplaceEmojis(delay = 150) {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(() => {
      this.replaceEmojisWithIcons();
      this.debounceTimer = null;
    }, delay);
  }

  // React安全的DOM操作检查
  isReactManagedElement(element) {
    if (!element || !this.reactSafeMode) return false;

    // 检查元素是否被React管理
    let current = element;
    while (current) {
      // 检查React内部属性
      if (current._reactInternalFiber ||
          current._reactInternalInstance ||
          current.__reactInternalInstance ||
          current._reactInternals ||
          Object.keys(current).some(key => key.startsWith('__react'))) {
        return true;
      }

      // 检查React相关的类名
      if (current.className && typeof current.className === 'string') {
        if (current.className.includes('react-')) {
          return true;
        }
      }

      current = current.parentElement;
    }

    return false;
  }

  // React安全的文本节点替换
  safeReplaceTextNode(textNode, fragment) {
    if (!textNode || !textNode.parentNode) return false;

    const parent = textNode.parentNode;

    // 在React安全模式下，跳过React管理的元素
    if (this.reactSafeMode && this.isReactManagedElement(parent)) {
      console.log('[EMOJI] 跳过React管理的元素:', parent.tagName || 'TEXT');
      return false;
    }

    return this.safeDOMOperation(() => {
      if (parent && textNode && parent.contains(textNode)) {
        parent.replaceChild(fragment, textNode);
        return true;
      }
      return false;
    }, 'React安全替换emoji时出错');
  }

  // 检测emoji支持情况
  checkEmojiSupport() {
    if (this.isEmojiSupported !== null) {
      return this.isEmojiSupported;
    }

    try {
      // 多重检测方法，确保在旧浏览器上正确识别emoji不支持
      
      // 方法1: 检测浏览器版本
      const userAgent = navigator.userAgent.toLowerCase();
      const isOldBrowser = (
        // IE浏览器
        /msie|trident/.test(userAgent) ||
        // 旧版Chrome (低于60)
        (/chrome\/([0-9]+)/.test(userAgent) && parseInt(RegExp.$1) < 60) ||
        // 旧版Firefox (低于60) 
        (/firefox\/([0-9]+)/.test(userAgent) && parseInt(RegExp.$1) < 60) ||
        // 旧版Safari (低于12)
        (/version\/([0-9]+).*safari/.test(userAgent) && parseInt(RegExp.$1) < 12) ||
        // 旧版Edge (低于79)
        (/edge\/([0-9]+)/.test(userAgent) && parseInt(RegExp.$1) < 79)
      );
      
      if (isOldBrowser) {
        console.log('[EMOJI] 检测到旧版浏览器，强制替换emoji');
        this.isEmojiSupported = false;
        return this.isEmojiSupported;
      }
      
      // 方法2: Canvas检测
      const canvas = document.createElement('canvas');
      if (!canvas.getContext) {
        // 不支持canvas，认为是旧浏览器
        this.isEmojiSupported = false;
        return this.isEmojiSupported;
      }
      
      const context = canvas.getContext('2d', { willReadFrequently: true });
      if (!context) {
        this.isEmojiSupported = false;
        return this.isEmojiSupported;
      }
      
      canvas.width = 32;
      canvas.height = 32;
      context.textBaseline = 'top';
      context.font = '32px Arial, sans-serif';
      
      // 绘制emoji
      context.fillText('🚀', 0, 0);
      const emojiData = context.getImageData(0, 0, 32, 32).data;
      
      // 清除并绘制替代字符
      context.clearRect(0, 0, 32, 32);
      context.fillText('□', 0, 0);
      const fallbackData = context.getImageData(0, 0, 32, 32).data;
      
      // 比较像素数据
      const isIdentical = this.arraysEqual(emojiData, fallbackData);
      this.isEmojiSupported = !isIdentical;
      
      // 方法3: 检查是否有非零像素（emoji应该有颜色）
      if (this.isEmojiSupported) {
        let hasColor = false;
        for (let i = 0; i < emojiData.length; i += 4) {
          // 检查RGB值是否不同（有颜色）
          if (emojiData[i] !== emojiData[i+1] || emojiData[i+1] !== emojiData[i+2]) {
            hasColor = true;
            break;
          }
        }
        this.isEmojiSupported = hasColor;
      }
      
      console.log('[EMOJI] Canvas检测结果:', {
        isIdentical,
        emojiSupported: this.isEmojiSupported,
        userAgent: userAgent.substring(0, 100)
      });
      
    } catch (error) {
      console.warn('Emoji支持检测失败:', error);
      this.isEmojiSupported = false;
    }

    return this.isEmojiSupported;
  }

  // 检测是否为内网环境
  checkIntranetEnvironment() {
    if (this.isIntranet !== null) {
      return this.isIntranet;
    }

    try {
      const hostname = window.location.hostname;
      const protocol = window.location.protocol;
      
      // 检查各种内网环境标识
      const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '0.0.0.0';
      const isPrivateIP = /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/.test(hostname);
      const isInternalDomain = hostname.includes('.local') || hostname.includes('.internal') || hostname.includes('.intranet');
      const isFileProtocol = protocol === 'file:';
      
      // 检查端口是否为开发环境常用端口
      const port = window.location.port;
      const isDevelopmentPort = ['3000', '3001', '3002', '5173', '8080', '8000'].includes(port);
      
      // 检查User Agent中是否有内网相关标识
      const userAgent = navigator.userAgent.toLowerCase();
      const hasIntranetUA = userAgent.includes('intranet') || userAgent.includes('internal');
      
      // 综合判断
      this.isIntranet = isLocalhost || isPrivateIP || isInternalDomain || 
                       isFileProtocol || isDevelopmentPort || hasIntranetUA;
      
      console.log('[DEBUG] 内网环境检测结果:', {
        hostname,
        isLocalhost,
        isPrivateIP,
        isInternalDomain,
        isDevelopmentPort,
        result: this.isIntranet
      });
      
    } catch (error) {
      console.warn('内网环境检测失败:', error);
      this.isIntranet = true; // 默认假设为内网环境，确保emoji替换
    }

    return this.isIntranet;
  }

  // 比较两个数组是否相等
  arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) return false;
    }
    return true;
  }

  // 替换页面中的emoji为FontAwesome图标
  replaceEmojisWithIcons(forceReplace = false) {
    // 临时禁用emoji替换以解决DOM冲突问题
    if (this.disabled) {
      console.log('[EMOJI] 功能已禁用，跳过替换');
      return;
    }

    // 检查是否需要替换emoji
    const emojiSupported = this.checkEmojiSupport();
    const isIntranet = this.checkIntranetEnvironment();
    const needsReplacement = !emojiSupported || isIntranet || forceReplace;
    
    if (!needsReplacement) {
      console.log('[✓] Emoji支持正常且非内网环境，无需替换');
      return;
    }

    const reason = forceReplace ? '强制替换' : 
                  (!emojiSupported ? 'Emoji不支持' : '内网环境');
    console.log('[↻] 开始替换emoji为FontAwesome图标 - 原因:', reason);
    
    // 获取所有文本节点
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      node => {
        // 跳过script和style标签内的文本
        const parent = node.parentElement;
        if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
          return NodeFilter.FILTER_REJECT;
        }

        // 在React安全模式下，跳过React管理的元素
        if (this.reactSafeMode && this.isReactManagedElement(parent)) {
          return NodeFilter.FILTER_REJECT;
        }

        return NodeFilter.FILTER_ACCEPT;
      },
      false
    );

    const textNodes = [];
    let node;
    while ((node = walker.nextNode())) {
      // 检查文本节点是否包含emoji
      const hasEmoji = Array.from(this.emojiToIcon.keys()).some(emoji => 
        node.textContent.includes(emoji)
      );
      if (hasEmoji) {
        textNodes.push(node);
      }
    }

    // 替换emoji
    textNodes.forEach(textNode => {
      let originalText = textNode.textContent;
      let needsUpdate = false;
      
      // 检查是否包含需要替换的emoji
      this.emojiToIcon.forEach((iconClass, emoji) => {
        if (originalText.includes(emoji)) {
          needsUpdate = true;
        }
      });

      if (needsUpdate) {
        const parent = textNode.parentNode;
        if (!parent) return;
        
        const fragment = document.createDocumentFragment();
        
        // 一次性处理所有emoji，避免多次替换导致的问题
        let processedText = originalText;
        
        // 收集所有需要替换的emoji位置
        const emojiMatches = [];
        this.emojiToIcon.forEach((iconClass, emoji) => {
          let index = 0;
          while ((index = processedText.indexOf(emoji, index)) !== -1) {
            emojiMatches.push({
              emoji,
              iconClass,
              start: index,
              end: index + emoji.length
            });
            index += emoji.length;
          }
        });
        
        // 按位置排序，从后往前处理避免位置偏移
        emojiMatches.sort((a, b) => b.start - a.start);
        
        // 移除重叠的匹配项（优先保留更长的emoji）
        const validMatches = [];
        for (const match of emojiMatches) {
          const hasOverlap = validMatches.some(existing => 
            (match.start < existing.end && match.end > existing.start)
          );
          if (!hasOverlap) {
            validMatches.push(match);
          }
        }
        
        // 如果有emoji需要替换
        if (validMatches.length > 0) {
          // 重新按位置从前往后排序
          validMatches.sort((a, b) => a.start - b.start);
          
          let lastIndex = 0;
          
          validMatches.forEach(match => {
            // 添加emoji前的文本
            if (match.start > lastIndex) {
              const textBefore = processedText.substring(lastIndex, match.start);
              if (textBefore) {
                fragment.appendChild(document.createTextNode(textBefore));
              }
            }
            
            // 添加图标
            const icon = document.createElement('i');
            icon.className = match.iconClass;
            icon.style.cssText = 'color: inherit; margin: 0 2px; font-size: inherit;';
            icon.setAttribute('aria-hidden', 'true');
            icon.title = match.emoji; // 保留原始emoji作为tooltip
            fragment.appendChild(icon);
            
            lastIndex = match.end;
          });
          
          // 添加最后剩余的文本
          if (lastIndex < processedText.length) {
            const textAfter = processedText.substring(lastIndex);
            if (textAfter) {
              fragment.appendChild(document.createTextNode(textAfter));
            }
          }
        }

        // 如果有内容需要替换，则使用React安全替换
        if (fragment.hasChildNodes()) {
          const success = this.safeReplaceTextNode(textNode, fragment);
          if (!success && this.reactSafeMode) {
            console.log('[EMOJI] React安全模式：跳过潜在冲突的DOM操作');
          }
        }
      }
    });

    console.log(`[✓] Emoji替换完成，处理了 ${textNodes.length} 个文本节点`);
  }

  // 初始化emoji兼容性处理
  init() {
    console.log('[EMOJI] 开始初始化emoji兼容性处理');
    
    // 立即执行一次检测和替换
    this.replaceEmojisWithIcons();
    
    // 等待DOM加载完成后再次执行
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        console.log('[EMOJI] DOM加载完成，执行emoji替换');
        setTimeout(() => this.replaceEmojisWithIcons(), 100);
      });
    } else {
      // DOM已经加载完成，延迟执行确保其他组件已渲染
      setTimeout(() => {
        console.log('[EMOJI] DOM已就绪，执行emoji替换');
        this.replaceEmojisWithIcons();
      }, 200);
    }

    // 等待字体加载完成后再次执行
    if (document.fonts && document.fonts.ready) {
      document.fonts.ready.then(() => {
        console.log('[EMOJI] 字体加载完成，执行emoji替换');
        setTimeout(() => this.replaceEmojisWithIcons(), 300);
      });
    }

    // 监听动态内容变化
    if (typeof MutationObserver !== 'undefined') {
      const observer = new MutationObserver((mutations) => {
        let hasNewContent = false;
        let hasTextNodes = false;
        
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            hasNewContent = true;
            // 检查是否有文本节点或包含emoji的节点
            Array.from(mutation.addedNodes).forEach(node => {
              if (node.nodeType === Node.TEXT_NODE || 
                  (node.textContent && Array.from(this.emojiToIcon.keys()).some(emoji => 
                    node.textContent.includes(emoji)))) {
                hasTextNodes = true;
              }
            });
          }
        });
        
        if (hasNewContent && hasTextNodes) {
          // 使用防抖机制避免频繁替换
          console.log('[EMOJI] 检测到新内容包含emoji，执行替换');
          this.debouncedReplaceEmojis();
        }
      });

      observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true,
        characterData: true
      });
    }

    // 定时检查机制（兜底方案）
    let checkCount = 0;
    const intervalCheck = setInterval(() => {
      checkCount++;
      console.log(`[EMOJI] 定时检查 #${checkCount}`);
      this.replaceEmojisWithIcons();
      
      // 检查10次后停止定时检查
      if (checkCount >= 10) {
        clearInterval(intervalCheck);
        console.log('[EMOJI] 定时检查完成');
      }
    }, 1000);
  }

  // 手动替换单个emoji
  replaceEmoji(emoji) {
    return this.emojiToIcon.get(emoji) || emoji;
  }

  // 获取环境信息
  getEnvironmentInfo() {
    return {
      emojiSupported: this.checkEmojiSupport(),
      isIntranet: this.checkIntranetEnvironment(),
      userAgent: navigator.userAgent,
      hostname: window.location.hostname,
      needsReplacement: !this.checkEmojiSupport() || this.checkIntranetEnvironment()
    };
  }

  // 强制替换所有emoji
  forceReplaceAllEmojis() {
    console.log('[FORCE] 强制替换所有emoji');
    this.replaceEmojisWithIcons(true);
  }

  // 测试emoji替换功能
  testEmojiReplacement() {
    console.log('[TEST] 开始emoji兼容性测试');
    const info = this.getEnvironmentInfo();
    console.log('[TEST] 环境信息:', info);
    
    // 创建测试元素
    const testDiv = document.createElement('div');
    testDiv.style.cssText = 'position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.9); color: white; padding: 15px; z-index: 9999; font-size: 13px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.5); font-family: monospace; max-width: 400px;';
    testDiv.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 10px;">📋 Emoji兼容性测试</div>
      <div>Emoji测试: 🚀 🔧 💻 🎯 ✨ 🔑 🌟 💡 🔥</div>
      <div style="margin: 8px 0;">环境: ${info.isIntranet ? '内网' : '外网'} | Emoji支持: ${info.emojiSupported ? '是' : '否'}</div>
      <div style="margin: 8px 0;">需要替换: ${info.needsReplacement ? '是' : '否'}</div>
      <div style="margin: 8px 0; font-size: 11px; color: #ccc;">浏览器: ${info.userAgent.substring(0, 50)}...</div>
      <button onclick="window.emojiCompatibility.forceReplaceAllEmojis()" style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">强制替换</button>
    `;
    
    document.body.appendChild(testDiv);
    
    // 10秒后安全移除测试元素
    setTimeout(() => {
      try {
        if (testDiv && testDiv.parentNode && testDiv.parentNode.contains(testDiv)) {
          testDiv.parentNode.removeChild(testDiv);
        }
      } catch (error) {
        console.warn('[TEST] 移除测试元素失败:', error.message);
      }
    }, 10000);
    
    console.log('[TEST] 测试元素已添加，10秒后自动移除');
  }
}

// 创建全局实例
const emojiCompatibility = new EmojiCompatibility();

// 自动初始化
emojiCompatibility.init();

// 开发模式下提供全局调试功能
if (typeof window !== 'undefined') {
  window.emojiCompatibility = emojiCompatibility;
  
  // 如果URL中包含debug参数，则显示测试界面
  if (window.location.search.includes('emoji_debug=true')) {
    setTimeout(() => {
      emojiCompatibility.testEmojiReplacement();
    }, 1000);
  }
}

export default emojiCompatibility; 