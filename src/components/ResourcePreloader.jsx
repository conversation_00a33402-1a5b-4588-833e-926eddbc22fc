import { useEffect } from 'react';

const ResourcePreloader = () => {
  useEffect(() => {
    // 预加载关键资源 - 移除字体CSS预加载，已在HTML中处理
    const preloadResources = [
      // 字体CSS已在HTML中预加载，避免重复
      // 预加载其他关键资源
    ];

    preloadResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      
      if (resource.endsWith('.css')) {
        link.as = 'style';
        link.href = resource;
      } else if (resource.endsWith('.js')) {
        link.as = 'script';
        link.href = resource;
      } else {
        link.as = 'image';
        link.href = resource;
      }
      
      document.head.appendChild(link);
    });

    // 内网部署：移除外部域名预连接，所有资源已本地化
    // 预连接到外部域名已禁用，确保内网环境正常运行
    console.log('✅ 内网模式：跳过外部域名预连接，所有资源已本地化');

    // 预加载关键 CSS
    const criticalCSS = `
      .critical-above-fold {
        will-change: transform;
        transform: translateZ(0);
      }
      .optimize-paint {
        contain: layout style paint;
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);

    // 清理函数
    return () => {
      try {
        // 安全清理预加载的资源
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        preloadLinks.forEach(link => {
          try {
            if (preloadResources.some(resource => link.href.includes(resource))) {
              if (link.parentNode && link.parentNode.contains(link)) {
                link.remove();
              }
            }
          } catch (error) {
            console.warn('清理预加载资源失败:', error.message);
          }
        });
      } catch (error) {
        console.warn('清理预加载资源时出错:', error.message);
      }
    };
  }, []);

  return null; // 这是一个无渲染组件
};

export default ResourcePreloader; 