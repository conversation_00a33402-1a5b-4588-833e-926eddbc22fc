#!/usr/bin/env node
/**
 * LDAP服务监控脚本
 * 监控LDAP认证服务的健康状态，在出现问题时自动处理
 */

import fetch from 'node-fetch';
import { spawn } from 'child_process';

const LDAP_SERVICE_URL = process.env.LDAP_SERVICE_URL || 'http://localhost:3002';
const CHECK_INTERVAL = parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10); // 30秒
const MAX_FAILURES = parseInt(process.env.MAX_HEALTH_FAILURES || '3', 10);

class LDAPServiceMonitor {
  constructor() {
    this.failureCount = 0;
    this.lastHealthyTime = new Date();
    this.isChecking = false;
  }

  async checkHealth() {
    if (this.isChecking) {
      console.log('[监控] 健康检查正在进行中，跳过此次检查');
      return;
    }

    this.isChecking = true;
    
    try {
      console.log(`[${new Date().toISOString()}] 开始健康检查...`);
      
      const response = await fetch(`${LDAP_SERVICE_URL}/health`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'LDAP-Service-Monitor/1.0'
        }
      });

      if (response.ok) {
        const healthData = await response.json();
        this.handleHealthyResponse(healthData);
      } else {
        this.handleUnhealthyResponse(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      this.handleUnhealthyResponse(error.message);
    } finally {
      this.isChecking = false;
    }
  }

  handleHealthyResponse(healthData) {
    if (this.failureCount > 0) {
      console.log(`✅ [监控] 服务已恢复健康 (之前失败 ${this.failureCount} 次)`);
    }
    
    this.failureCount = 0;
    this.lastHealthyTime = new Date();
    
    // 记录详细健康信息
    console.log(`🔍 [监控] 服务状态: ${healthData.status}`);
    console.log(`📊 [监控] 内存使用: ${Math.round(healthData.memory?.rss / 1024 / 1024)}MB`);
    console.log(`⏱️  [监控] 运行时间: ${Math.round(healthData.uptime)}秒`);
    
    if (healthData.ldap) {
      console.log(`🔐 [监控] LDAP连接状态: ${healthData.ldap.status}`);
      if (healthData.ldap.server) {
        console.log(`🌐 [监控] LDAP服务器: ${healthData.ldap.server}`);
      }
    }
  }

  handleUnhealthyResponse(error) {
    this.failureCount++;
    console.error(`❌ [监控] 健康检查失败 (${this.failureCount}/${MAX_FAILURES}): ${error}`);
    
    if (this.failureCount >= MAX_FAILURES) {
      console.error(`🚨 [监控] 服务连续失败 ${this.failureCount} 次，尝试重启服务...`);
      this.attemptServiceRestart();
    }
  }

  async attemptServiceRestart() {
    try {
      console.log('🔄 [监控] 尝试通过PM2重启LDAP服务...');
      
      const pm2Process = spawn('pm2', ['restart', 'ynnx-ldap-auth'], {
        stdio: 'pipe'
      });

      pm2Process.stdout.on('data', (data) => {
        console.log(`[PM2] ${data.toString().trim()}`);
      });

      pm2Process.stderr.on('data', (data) => {
        console.error(`[PM2错误] ${data.toString().trim()}`);
      });

      pm2Process.on('close', (code) => {
        if (code === 0) {
          console.log('✅ [监控] PM2重启命令执行成功');
          this.failureCount = 0; // 重置失败计数
          
          // 等待服务启动后再次检查
          setTimeout(() => {
            this.checkHealth();
          }, 10000);
        } else {
          console.error(`❌ [监控] PM2重启失败，退出码: ${code}`);
        }
      });

    } catch (error) {
      console.error(`❌ [监控] 重启服务时出错: ${error.message}`);
    }
  }

  start() {
    console.log(`🚀 [监控] LDAP服务监控启动`);
    console.log(`🎯 [监控] 目标服务: ${LDAP_SERVICE_URL}`);
    console.log(`⏰ [监控] 检查间隔: ${CHECK_INTERVAL}ms`);
    console.log(`🔥 [监控] 失败阈值: ${MAX_FAILURES}次`);
    
    // 立即执行一次健康检查
    this.checkHealth();
    
    // 设置定期检查
    setInterval(() => {
      this.checkHealth();
    }, CHECK_INTERVAL);

    // 处理进程信号
    process.on('SIGINT', () => {
      console.log('\n🛑 [监控] 收到SIGINT信号，正在关闭监控器...');
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 [监控] 收到SIGTERM信号，正在关闭监控器...');
      process.exit(0);
    });

    // 防止未处理的错误导致监控器退出
    process.on('uncaughtException', (error) => {
      console.error('❌ [监控] 未捕获的异常:', error.message);
      console.error('堆栈信息:', error.stack);
      // 不要退出，继续监控
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ [监控] 未处理的Promise拒绝:', reason);
      console.error('Promise:', promise);
      // 不要退出，继续监控
    });
  }

  stop() {
    console.log('🛑 [监控] 停止LDAP服务监控');
    process.exit(0);
  }
}

// 如果直接运行此脚本，启动监控器
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new LDAPServiceMonitor();
  monitor.start();
}

export default LDAPServiceMonitor; 