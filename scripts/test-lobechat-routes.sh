#!/bin/bash

echo "🔍 LobeChat 路由功能测试"
echo "================================"

# LobeChat 的主要路由
ROUTES=(
    "/chat/chat"
    "/chat/files" 
    "/chat/image"
    "/chat/discover"
)

echo "1. 测试所有 LobeChat 路由访问..."

ROUTE_SUCCESS=0
for route in "${ROUTES[@]}"; do
    echo -n "   测试 $route ... "
    
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://***********$route" -k)
    
    if [ "$STATUS" = "200" ]; then
        echo "✅ HTTP $STATUS"
        ROUTE_SUCCESS=$((ROUTE_SUCCESS + 1))
    else
        echo "❌ HTTP $STATUS"
    fi
done

echo ""
echo "2. 测试路径重写功能..."

# 检查每个路由的路径重写
for route in "${ROUTES[@]}"; do
    echo "   检查 $route 的路径重写:"
    
    # 检查静态资源路径
    STATIC_REWRITTEN=$(curl -s "https://***********$route" -k | grep -c '/chat/_next/static/')
    echo "     静态资源重写: $STATIC_REWRITTEN 个"
    
    # 检查 base 标签
    BASE_TAG=$(curl -s "https://***********$route" -k | grep -c '<base href="/chat/">')
    echo "     base 标签: $([ "$BASE_TAG" -gt 0 ] && echo "✅ 已注入" || echo "❌ 未注入")"
    
    # 检查路由修复脚本
    ROUTE_SCRIPT=$(curl -s "https://***********$route" -k | grep -c "LobeChat 路由修复脚本")
    echo "     路由修复脚本: $([ "$ROUTE_SCRIPT" -gt 0 ] && echo "✅ 已注入" || echo "❌ 未注入")"
    
    echo ""
done

echo "3. 测试重定向功能..."

# 测试根路径重定向
REDIRECT_TESTS=(
    "/chat"
    "/files"
    "/image" 
    "/discover"
)

for path in "${REDIRECT_TESTS[@]}"; do
    echo -n "   测试 $path 重定向 ... "
    
    REDIRECT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://***********$path" -k)
    
    if [ "$REDIRECT_STATUS" = "301" ] || [ "$REDIRECT_STATUS" = "302" ]; then
        echo "✅ HTTP $REDIRECT_STATUS (重定向正常)"
    elif [ "$REDIRECT_STATUS" = "200" ]; then
        echo "⚠️  HTTP $REDIRECT_STATUS (直接访问，可能需要检查)"
    else
        echo "❌ HTTP $REDIRECT_STATUS"
    fi
done

echo ""
echo "4. 测试 API 路由..."

# 测试关键 API
API_TESTS=(
    "/chat/api/auth/session"
    "/chat/api/auth/providers"
    "/chat/api/chat"
)

API_SUCCESS=0
for api in "${API_TESTS[@]}"; do
    echo -n "   测试 $api ... "
    
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://***********$api" -k)
    
    if [ "$STATUS" = "200" ]; then
        echo "✅ HTTP $STATUS"
        API_SUCCESS=$((API_SUCCESS + 1))
    else
        echo "❌ HTTP $STATUS"
    fi
done

echo ""
echo "5. 创建路由测试页面..."

# 创建一个测试页面来验证客户端路由
cat > /tmp/route-test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>LobeChat 路由测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin: 10px 0; }
        #log { background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>LobeChat 路由测试</h1>
    
    <div class="test-section info">
        <h3>路由导航测试</h3>
        <p>点击下面的按钮测试不同路由的导航：</p>
        <button onclick="testRoute('/chat/chat')">聊天页面</button>
        <button onclick="testRoute('/chat/files')">文件页面</button>
        <button onclick="testRoute('/chat/image')">图像页面</button>
        <button onclick="testRoute('/chat/discover')">发现页面</button>
        <button onclick="testRoute('/chat/')">根路径</button>
    </div>
    
    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h3>当前测试的路由</h3>
        <div id="current-route">点击上面的按钮开始测试</div>
        <iframe id="test-iframe" src="about:blank"></iframe>
    </div>

    <script>
        const log = document.getElementById('log');
        const currentRoute = document.getElementById('current-route');
        const iframe = document.getElementById('test-iframe');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        function testRoute(route) {
            addLog(`开始测试路由: ${route}`, 'info');
            currentRoute.textContent = `正在测试: ${route}`;
            
            // 设置 iframe 源
            iframe.src = route;
            
            // 监听 iframe 加载
            iframe.onload = function() {
                addLog(`路由 ${route} 加载完成`, 'success');
                
                // 尝试检查 iframe 内容
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        const title = iframeDoc.title;
                        addLog(`页面标题: ${title}`, 'info');
                        
                        // 检查是否有错误
                        const errorElements = iframeDoc.querySelectorAll('.error, .next-error-h1');
                        if (errorElements.length > 0) {
                            addLog(`发现 ${errorElements.length} 个错误元素`, 'error');
                        } else {
                            addLog('页面加载正常，未发现错误', 'success');
                        }
                    }
                } catch (e) {
                    addLog(`无法检查 iframe 内容: ${e.message}`, 'info');
                }
            };
            
            iframe.onerror = function() {
                addLog(`路由 ${route} 加载失败`, 'error');
            };
        }
        
        // 页面加载完成后的初始化
        addLog('路由测试页面已加载', 'success');
        addLog('请点击上面的按钮测试不同的路由', 'info');
    </script>
</body>
</html>
EOF

cp /tmp/route-test.html public/lobechat-route-test.html
echo "   ✅ 路由测试页面已创建: https://***********/lobechat-route-test.html"

echo ""
echo "📊 测试结果总结:"
echo "================================"
echo "   路由访问成功: $ROUTE_SUCCESS/${#ROUTES[@]}"
echo "   API 访问成功: $API_SUCCESS/${#API_TESTS[@]}"

TOTAL_SUCCESS=$((ROUTE_SUCCESS + API_SUCCESS))
TOTAL_TESTS=$((${#ROUTES[@]} + ${#API_TESTS[@]}))
SUCCESS_RATE=$(( TOTAL_SUCCESS * 100 / TOTAL_TESTS ))

echo "   总体成功率: $SUCCESS_RATE%"

if [ $SUCCESS_RATE -ge 90 ]; then
    echo ""
    echo "🎉 LobeChat 路由功能测试通过！"
    echo ""
    echo "✅ 所有主要功能正常:"
    echo "   • 所有路由都可以正常访问"
    echo "   • 路径重写机制工作正常"
    echo "   • JavaScript 路由修复已注入"
    echo "   • API 路由代理正常"
    echo ""
    echo "🌐 现在可以在 LobeChat 中自由导航了！"
    echo "   主平台: https://***********/#chat"
    echo "   路由测试: https://***********/lobechat-route-test.html"
else
    echo ""
    echo "⚠️  部分路由功能仍有问题，建议进一步检查"
fi
