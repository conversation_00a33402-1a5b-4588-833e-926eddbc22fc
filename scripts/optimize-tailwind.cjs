#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Tailwind CSS 优化脚本
 * 分析项目中使用的类，生成优化建议
 */

class TailwindOptimizer {
  constructor() {
    this.usedClasses = new Set();
    this.potentialClasses = new Set();
    this.redundantClasses = new Set();
    this.componentFiles = [];
    this.cssFiles = [];
  }

  // 扫描项目文件
  scanProject() {
    console.log('🔍 扫描项目文件...');
    
    // 扫描 JSX/JS 文件
    const jsxFiles = glob.sync('src/**/*.{jsx,js}', { 
      ignore: ['src/**/*.test.{jsx,js}', 'src/**/*.spec.{jsx,js}'] 
    });
    
    // 扫描 CSS 文件
    const cssFiles = glob.sync('src/**/*.{css,scss}');
    
    this.componentFiles = jsxFiles;
    this.cssFiles = cssFiles;
    
    console.log(`📁 找到 ${jsxFiles.length} 个组件文件`);
    console.log(`📁 找到 ${cssFiles.length} 个样式文件`);
  }

  // 提取类名
  extractClasses() {
    console.log('📊 提取 Tailwind 类名...');
    
    // Tailwind 类名匹配模式
    const classPatterns = [
      // className="..."
      /className\s*=\s*[`"']([^`"']*)[`"']/g,
      // className={`...`} 或 className={"..."}
      /className\s*=\s*{[`"']([^`"']*)[`"']}/g,
      // 模板字符串中的类名
      /className\s*=\s*{`([^`]*)`}/g,
      // class="..." (HTML)
      /class\s*=\s*[`"']([^`"']*)[`"']/g
    ];

    this.componentFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        classPatterns.forEach(pattern => {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            const classString = match[1];
            this.parseClassString(classString);
          }
        });
      } catch (error) {
        console.warn(`⚠️  读取文件失败: ${filePath}`, error.message);
      }
    });

    console.log(`✅ 提取到 ${this.usedClasses.size} 个不同的类名`);
  }

  // 解析类名字符串
  parseClassString(classString) {
    if (!classString) return;
    
    // 处理模板字符串变量
    const cleanString = classString
      .replace(/\$\{[^}]*\}/g, '') // 移除模板字符串变量
      .replace(/\s+/g, ' ') // 规范化空格
      .trim();
    
    // 分割类名
    const classes = cleanString.split(/\s+/).filter(Boolean);
    
    classes.forEach(className => {
      if (this.isTailwindClass(className)) {
        this.usedClasses.add(className);
      }
    });
  }

  // 检查是否为 Tailwind 类名
  isTailwindClass(className) {
    // 常见的 Tailwind 前缀
    const tailwindPrefixes = [
      // 布局
      'container', 'box-', 'block', 'inline', 'flex', 'grid', 'table', 'hidden',
      // 间距
      'p-', 'px-', 'py-', 'pt-', 'pr-', 'pb-', 'pl-',
      'm-', 'mx-', 'my-', 'mt-', 'mr-', 'mb-', 'ml-',
      'space-', 'gap-',
      // 尺寸
      'w-', 'min-w-', 'max-w-', 'h-', 'min-h-', 'max-h-',
      // 颜色
      'text-', 'bg-', 'border-', 'ring-', 'shadow-',
      // 字体
      'font-', 'text-', 'leading-', 'tracking-', 'uppercase', 'lowercase', 'capitalize',
      // 边框
      'border', 'rounded', 'divide-',
      // 定位
      'absolute', 'relative', 'fixed', 'sticky', 'static',
      'top-', 'right-', 'bottom-', 'left-', 'inset-', 'z-',
      // 动画
      'animate-', 'transition', 'duration-', 'ease-', 'delay-',
      // 变换
      'transform', 'rotate-', 'scale-', 'translate-', 'skew-',
      // 响应式
      'sm:', 'md:', 'lg:', 'xl:', '2xl:',
      // 伪类
      'hover:', 'focus:', 'active:', 'disabled:', 'group-hover:',
      // Flexbox
      'justify-', 'items-', 'content-', 'self-', 'flex-', 'order-',
      // Grid
      'grid-cols-', 'col-span-', 'row-span-', 'gap-',
      // 溢出
      'overflow-', 'whitespace-',
      // 可见性
      'visible', 'invisible', 'opacity-'
    ];

    return tailwindPrefixes.some(prefix => 
      className.startsWith(prefix) || className === prefix.replace('-', '')
    );
  }

  // 分析重复和冗余
  analyzeRedundancy() {
    console.log('🔍 分析类名使用模式...');
    
    const classFrequency = new Map();
    
    this.componentFiles.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        this.usedClasses.forEach(className => {
          const regex = new RegExp(`\\b${className}\\b`, 'g');
          const matches = content.match(regex);
          if (matches) {
            classFrequency.set(className, (classFrequency.get(className) || 0) + matches.length);
          }
        });
      } catch (error) {
        console.warn(`⚠️  分析文件失败: ${filePath}`);
      }
    });

    // 找出高频使用的类（可能需要提取为组件类）
    const highFrequencyClasses = Array.from(classFrequency.entries())
      .filter(([, frequency]) => frequency >= 5)
      .sort((a, b) => b[1] - a[1]);

    return { classFrequency, highFrequencyClasses };
  }

  // 生成优化建议
  generateOptimizationSuggestions(analysis) {
    const suggestions = [];

    // 高频类名建议
    if (analysis.highFrequencyClasses.length > 0) {
      suggestions.push({
        type: 'component-extraction',
        title: '🔄 组件提取建议',
        description: '以下类名使用频率较高，建议提取为可复用组件：',
        items: analysis.highFrequencyClasses.slice(0, 10).map(([className, frequency]) => 
          `${className} (使用 ${frequency} 次)`
        )
      });
    }

    // 相似类名合并建议
    const similarClasses = this.findSimilarClasses();
    if (similarClasses.length > 0) {
      suggestions.push({
        type: 'class-consolidation',
        title: '🎯 类名合并建议',
        description: '以下相似类名可能可以合并：',
        items: similarClasses
      });
    }

    return suggestions;
  }

  // 找出相似的类名
  findSimilarClasses() {
    const similar = [];
    const classArray = Array.from(this.usedClasses);
    
    // 按前缀分组
    const groups = {};
    classArray.forEach(className => {
      const prefix = className.split('-')[0];
      if (!groups[prefix]) groups[prefix] = [];
      groups[prefix].push(className);
    });

    // 找出每组中的相似项
    Object.entries(groups).forEach(([prefix, classes]) => {
      if (classes.length > 3) {
        similar.push(`${prefix}-* 类 (${classes.length} 个): ${classes.slice(0, 5).join(', ')}${classes.length > 5 ? '...' : ''}`);
      }
    });

    return similar;
  }

  // 生成优化的 Tailwind 配置
  generateOptimizedConfig() {
    const usedColors = new Set();
    const usedSpacing = new Set();
    const usedBreakpoints = new Set();

    this.usedClasses.forEach(className => {
      // 提取颜色
      const colorMatch = className.match(/(text|bg|border)-(red|blue|green|yellow|purple|pink|indigo|gray|black|white)-(\d+)/);
      if (colorMatch) {
        usedColors.add(`${colorMatch[2]}-${colorMatch[3]}`);
      }

      // 提取间距
      const spacingMatch = className.match(/(p|m|w|h)-(\d+)/);
      if (spacingMatch) {
        usedSpacing.add(spacingMatch[2]);
      }

      // 提取断点
      const breakpointMatch = className.match(/^(sm|md|lg|xl|2xl):/);
      if (breakpointMatch) {
        usedBreakpoints.add(breakpointMatch[1]);
      }
    });

    return {
      colors: Array.from(usedColors).sort(),
      spacing: Array.from(usedSpacing).sort((a, b) => parseInt(a) - parseInt(b)),
      breakpoints: Array.from(usedBreakpoints)
    };
  }

  // 运行完整分析
  async run() {
    console.log('🚀 开始 Tailwind CSS 优化分析...\n');

    try {
      // 1. 扫描项目
      this.scanProject();
      
      // 2. 提取类名
      this.extractClasses();
      
      // 3. 分析模式
      const analysis = this.analyzeRedundancy();
      
      // 4. 生成建议
      const suggestions = this.generateOptimizationSuggestions(analysis);
      
      // 5. 生成优化配置
      const optimizedConfig = this.generateOptimizedConfig();

      // 6. 输出报告
      this.generateReport(analysis, suggestions, optimizedConfig);

    } catch (error) {
      console.error('❌ 分析过程中出错:', error);
    }
  }

  // 生成报告
  generateReport(analysis, suggestions, optimizedConfig) {
    console.log('\n📊 === Tailwind CSS 优化报告 ===\n');

    // 基本统计
    console.log('📈 基本统计:');
    console.log(`   • 扫描文件: ${this.componentFiles.length} 个`);
    console.log(`   • 使用的类名: ${this.usedClasses.size} 个`);
    console.log(`   • 分析的组件文件: ${this.componentFiles.length} 个\n`);

    // 高频类名
    if (analysis.highFrequencyClasses.length > 0) {
      console.log('🔥 高频使用类名 (前10):');
      analysis.highFrequencyClasses.slice(0, 10).forEach(([className, frequency]) => {
        console.log(`   • ${className}: ${frequency} 次`);
      });
      console.log('');
    }

    // 优化建议
    if (suggestions.length > 0) {
      console.log('💡 优化建议:');
      suggestions.forEach(suggestion => {
        console.log(`\n${suggestion.title}`);
        console.log(`   ${suggestion.description}`);
        suggestion.items.forEach(item => {
          console.log(`   • ${item}`);
        });
      });
      console.log('');
    }

    // 配置优化
    console.log('⚙️  优化的 Tailwind 配置建议:');
    console.log(`   • 使用的颜色: ${optimizedConfig.colors.length} 种`);
    console.log(`   • 使用的间距: ${optimizedConfig.spacing.length} 种`);
    console.log(`   • 使用的断点: ${optimizedConfig.breakpoints.length} 个`);

    // 保存详细报告
    const reportData = {
      timestamp: new Date().toISOString(),
      usedClasses: Array.from(this.usedClasses).sort(),
      analysis,
      suggestions,
      optimizedConfig
    };

    const reportPath = 'tailwind-optimization-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`\n💾 详细报告已保存到: ${reportPath}`);

    console.log('\n✅ 分析完成！');
  }
}

// 运行分析
if (require.main === module) {
  const optimizer = new TailwindOptimizer();
  optimizer.run().catch(console.error);
}

module.exports = TailwindOptimizer; 