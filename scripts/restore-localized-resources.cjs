#!/usr/bin/env node

/**
 * 恢复原始文件脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 恢复原始文件...');

const backupFiles = [
  'src/utils/browserCompatibility.js.backup',
  'index.html.backup',
  
  'src/server/ldapAuthServer.js.backup',
  'public/assets/fonts/fontawesome.css.backup'
];

backupFiles.forEach(backupFile => {
  if (fs.existsSync(backupFile)) {
    const originalFile = backupFile.replace('.backup', '');
    fs.copyFileSync(backupFile, originalFile);
    fs.unlinkSync(backupFile);
    console.log(`✅ 已恢复 ${originalFile}`);
  }
});

// 删除本地资源目录
const localResourcesDir = 'public/local-resources';
if (fs.existsSync(localResourcesDir)) {
  fs.rmSync(localResourcesDir, { recursive: true, force: true });
  console.log('✅ 已删除本地资源目录');
}

console.log('\n🎉 恢复完成！');
