#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/components/Footer.jsx',
  'src/components/APIKeySection.jsx',
  'src/components/NewsSection.jsx',
  'src/components/BrowserCompatibilityCheck.jsx',
  
  'src/components/DocumentationSection.jsx',
  'src/components/LoginModal.jsx',
  'src/components/DownloadsSection.jsx',
  'src/components/LiteLLMConfig.jsx',
  'src/components/AIAssistantSection.jsx',
  'src/components/UserStatusIndicator.jsx'
];

function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 修复常见的语法错误
    const fixes = [
      // 修复 div}}}} 等问题
      {
        pattern: /<(\w+)(}+)(\s*)/g,
        replacement: '<$1$3'
      },
      // 修复多余的括号
      {
        pattern: /\s+key={[^}]*}}}+/g,
        replacement: (match) => {
          const cleaned = match.replace(/}+$/, '}');
          return cleaned;
        }
      },
      // 修复 style 属性中的多余括号
      {
        pattern: /style={{[^}]*}}+}/g,
        replacement: (match) => {
          return match.replace(/}+$/, '}');
        }
      },
      // 修复 className 中的多余括号
      {
        pattern: /className={[^}]*}}+/g,
        replacement: (match) => {
          return match.replace(/}+$/, '}');
        }
      },
      // 修复空的属性
      {
        pattern: /\s+\w+=\{\s*\}/g,
        replacement: ''
      }
    ];

    let originalContent = content;
    
    fixes.forEach(fix => {
      if (typeof fix.replacement === 'function') {
        content = content.replace(fix.pattern, fix.replacement);
      } else {
        content = content.replace(fix.pattern, fix.replacement);
      }
    });

    // 检查是否有修改
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    } else {
      console.log(`⚪ 无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔧 开始修复语法错误...\n');

let fixedCount = 0;
let totalFiles = filesToFix.length;

filesToFix.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    if (fixSyntaxErrors(filePath)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  文件不存在: ${filePath}`);
  }
});

console.log(`\n📊 修复完成!`);
console.log(`   - 总文件数: ${totalFiles}`);
console.log(`   - 已修复: ${fixedCount}`);
console.log(`   - 跳过: ${totalFiles - fixedCount}`); 