#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要处理的文件列表
const filesToProcess = [
  'src/components/FeaturesGuideSection.jsx',
  'src/components/Footer.jsx',
  'src/components/APIKeySection.jsx',
  'src/components/NewsSection.jsx',
  'src/components/BrowserCompatibilityCheck.jsx',
  
  'src/components/DocumentationSection.jsx',
  'src/components/LoginModal.jsx',
  'src/components/DownloadsSection.jsx',
  'src/components/LiteLLMConfig.jsx',
  'src/components/AIAssistantSection.jsx',
  'src/components/UserStatusIndicator.jsx'
];

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 移除 framer-motion 导入
    if (content.includes('framer-motion')) {
      content = content.replace(/import\s+{[^}]*}\s+from\s+['"]framer-motion['"];?\n?/g, '');
      content = content.replace(/import\s+\*\s+as\s+\w+\s+from\s+['"]framer-motion['"];?\n?/g, '');
      modified = true;
    }

    // 替换 motion 组件为普通 div
    if (content.includes('<motion.')) {
      content = content.replace(/<motion\.(\w+)/g, '<$1');
      content = content.replace(/<\/motion\.\w+>/g, (match) => {
        const tag = match.match(/motion\.(\w+)/)[1];
        return `</${tag}>`;
      });
      modified = true;
    }

    // 移除 motion 属性
    const motionProps = [
      'initial', 'animate', 'exit', 'transition', 'variants',
      'whileHover', 'whileTap', 'whileInView', 'viewport',
      'drag', 'dragConstraints', 'onDrag', 'style'
    ];

    motionProps.forEach(prop => {
      // 简单的属性移除（单行）
      const simpleRegex = new RegExp(`\\s+${prop}=\\{[^}]*\\}`, 'g');
      if (content.match(simpleRegex)) {
        content = content.replace(simpleRegex, '');
        modified = true;
      }

      // 复杂的属性移除（多行）
      const multilineRegex = new RegExp(`\\s+${prop}=\\{\\{[\\s\\S]*?\\}\\}`, 'g');
      if (content.match(multilineRegex)) {
        content = content.replace(multilineRegex, '');
        modified = true;
      }
    });

    // 移除 AnimatePresence 组件
    if (content.includes('AnimatePresence')) {
      content = content.replace(/<AnimatePresence[^>]*>/g, '<>');
      content = content.replace(/<\/AnimatePresence>/g, '</>');
      modified = true;
    }

    if (modified) {
      // 添加基本的 CSS 动画类
      if (!content.includes('animate-fade-in-up') && !content.includes('className="')) {
        // 简单地为根元素添加动画类
        content = content.replace(/className="([^"]*)"/, 'className="$1 animate-fade-in-up"');
      }

      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已处理: ${filePath}`);
      return true;
    } else {
      console.log(`⚪ 无需处理: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 处理失败 ${filePath}:`, error.message);
    return false;
  }
}

console.log('🚀 开始批量移除 framer-motion...\n');

let processedCount = 0;
let totalFiles = filesToProcess.length;

filesToProcess.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    if (processFile(filePath)) {
      processedCount++;
    }
  } else {
    console.log(`⚠️  文件不存在: ${filePath}`);
  }
});

console.log(`\n📊 处理完成!`);
console.log(`   - 总文件数: ${totalFiles}`);
console.log(`   - 已处理: ${processedCount}`);
console.log(`   - 跳过: ${totalFiles - processedCount}`);

console.log('\n💡 提示:');
console.log('   - 所有 motion 组件已替换为普通 HTML 元素');
console.log('   - motion 属性已移除');
console.log('   - 建议手动检查组件功能是否正常');
console.log('   - 可以添加 CSS 动画类来保持视觉效果'); 