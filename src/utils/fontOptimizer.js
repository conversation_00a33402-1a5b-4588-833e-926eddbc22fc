// 字体优化工具 - 按需加载字体
class FontOptimizer {
  constructor() {
    this.loadedFonts = new Set();
    this.fontObserver = null;
  }

  // 只加载必要的 FontAwesome 图标
  loadEssentialIcons() {
    const essentialIconClasses = [
      'fa-user-circle',
      'fa-sign-out-alt',
      'fa-code',
      'fa-robot',
      'fa-bolt',
      'fa-shield-alt',
      'fa-play',
      'fa-comments',
      'fa-cog',
      'fa-lightbulb',
      'fa-spinner',
      'fa-bullhorn',
      'fa-rocket',
      'fa-chart-line',
      'fa-users',
      'fa-keyboard',
      'fa-brain',
      'fa-tools',
      'fa-cloud-download-alt',
      'fa-key',
      'fa-download',
      'fa-book-open',
      'fa-question-circle',
      'fa-info-circle',
      'fa-clock',
      'fa-eye',
      'fa-eye-slash',
      'fa-check-circle',
      'fa-exclamation-circle'
    ];

    // 创建一个包含必要图标的精简CSS
    const minimalFontAwesome = `
      @font-face {
        font-family: "Font Awesome 5 Free";
        src: url("/assets/webfonts/fa-solid-900.woff2") format("woff2");
        font-weight: 900;
        font-display: swap;
      }
      
      .fa, .fas {
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        font-style: normal;
        font-variant: normal;
        text-rendering: auto;
        line-height: 1;
      }
      
      ${essentialIconClasses.map(iconClass => `
        .${iconClass}:before { content: "\\${this.getIconUnicode(iconClass)}"; }
      `).join('')}
    `;

    this.injectCSS(minimalFontAwesome, 'minimal-fontawesome');
  }

  // 获取图标对应的 Unicode
  getIconUnicode(iconClass) {
    const iconMap = {
      'fa-user-circle': 'f2bd',
      'fa-sign-out-alt': 'f2f5',
      'fa-code': 'f121',
      'fa-robot': 'f544',
      'fa-bolt': 'f0e7',
      'fa-shield-alt': 'f3ed',
      'fa-play': 'f04b',
      'fa-comments': 'f086',
      'fa-cog': 'f013',
      'fa-lightbulb': 'f0eb',
      'fa-spinner': 'f110',
      'fa-bullhorn': 'f0a1',
      'fa-rocket': 'f135',
      'fa-chart-line': 'f201',
      'fa-users': 'f0c0',
      'fa-keyboard': 'f11c',
      'fa-brain': 'f5dc',
      'fa-tools': 'f7d9',
      'fa-cloud-download-alt': 'f381',
      'fa-key': 'f084',
      'fa-download': 'f019',
      'fa-book-open': 'f518',
      'fa-question-circle': 'f059',
      'fa-info-circle': 'f05a',
      'fa-clock': 'f017',
      'fa-eye': 'f06e',
      'fa-eye-slash': 'f070',
      'fa-check-circle': 'f058',
      'fa-exclamation-circle': 'f06a'
    };
    return iconMap[iconClass] || 'f000';
  }

  // 注入 CSS 到页面
  injectCSS(css, id) {
    if (document.getElementById(id)) return;
    
    const style = document.createElement('style');
    style.id = id;
    style.textContent = css;
    document.head.appendChild(style);
  }

  // 预加载关键字体
  preloadFonts() {
    const criticalFonts = [
      '/assets/webfonts/fa-solid-900.woff2'
    ];

    criticalFonts.forEach(fontUrl => {
      if (this.loadedFonts.has(fontUrl)) return;
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      link.href = fontUrl;
      document.head.appendChild(link);
      
      this.loadedFonts.add(fontUrl);
    });
  }

  // 初始化字体优化
  init() {
    // 替换完整的 FontAwesome CSS
    this.disableFullFontAwesome();
    
    // 加载精简版本
    this.loadEssentialIcons();
    
    // 预加载关键字体
    this.preloadFonts();
  }

  // 禁用完整的 FontAwesome CSS
  disableFullFontAwesome() {
    const fullFALinks = document.querySelectorAll('link[href*="fontawesome.css"]');
    fullFALinks.forEach(link => {
      link.disabled = true;
    });
  }
}

const fontOptimizer = new FontOptimizer();
export default fontOptimizer; 