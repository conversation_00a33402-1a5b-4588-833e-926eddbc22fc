{"timestamp": "2025-06-19T15:49:51.019Z", "usedClasses": ["absolute", "animate-bounce", "animate-fade-in-up", "animate-ping", "animate-pulse", "animate-scale-in", "animate-spin", "bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)]", "bg-[size:50px_50px]", "bg-black", "bg-black/80", "bg-blue-400", "bg-blue-500/10", "bg-blue-500/5", "bg-blue-900/20", "bg-blue-900/50", "bg-clip-text", "bg-cyan-400", "bg-cyan-500", "bg-cyan-500/10", "bg-cyan-500/20", "bg-cyan-500/5", "bg-cyan-600", "bg-cyan-900/90", "bg-gradient-radial", "bg-gradient-to-b", "bg-gradient-to-br", "bg-gradient-to-r", "bg-gradient-to-t", "bg-gray-400", "bg-gray-700", "bg-gray-700/50", "bg-gray-800", "bg-gray-800/30", "bg-gray-800/50", "bg-gray-900", "bg-gray-900/50", "bg-gray-900/80", "bg-gray-900/95", "bg-green-400", "bg-green-500", "bg-green-500/20", "bg-green-900/50", "bg-opacity-75", "bg-purple-400", "bg-purple-500/10", "bg-purple-500/20", "bg-purple-500/5", "bg-red-500", "bg-red-500/20", "bg-red-600", "bg-red-900/50", "bg-transparent", "bg-white/10", "bg-white/20", "bg-yellow-400", "bg-yellow-500", "bg-yellow-500/10", "bg-yellow-500/20", "bg-yellow-500/90", "bg-yellow-900/30", "block", "border", "border-2", "border-4", "border-b", "border-b-2", "border-black", "border-blue-300", "border-blue-500/20", "border-blue-500/30", "border-blue-700", "border-blue-700/50", "border-cyan-500", "border-cyan-500/20", "border-cyan-500/30", "border-gray-400", "border-gray-600", "border-gray-700", "border-gray-800", "border-green-500/50", "border-green-700", "border-l-4", "border-l-gray-800", "border-purple-500/20", "border-red-500/50", "border-red-700", "border-t", "border-t-transparent", "border-transparent", "border-yellow-400", "border-yellow-500/30", "border-yellow-500/50", "border-yellow-600/30", "bottom-0", "bottom-1/3", "bottom-1/4", "bottom-4", "bottom-8", "col-span-full", "disabled:bg-cyan-700", "disabled:bg-gray-800", "disabled:cursor-not-allowed", "disabled:from-gray-600", "disabled:opacity-50", "disabled:to-gray-700", "duration-200", "duration-300", "duration-500", "fixed", "flex", "flex-1", "flex-col", "flex-grow", "flex-shrink-0", "flex-wrap", "focus:border-cyan-500", "focus:border-cyan-500/50", "focus:outline-none", "font-black", "font-bold", "font-medium", "font-mono", "font-semibold", "gap-1", "gap-12", "gap-2", "gap-3", "gap-4", "gap-6", "gap-8", "gap-[2px]", "grid", "grid-cols-1", "grid-cols-2", "group-hover:opacity-100", "group-hover:text-cyan-300", "group-hover:text-cyan-400", "group-hover:translate-x-1", "h-1", "h-1.5", "h-10", "h-16", "h-2", "h-20", "h-3", "h-4", "h-5", "h-6", "h-64", "h-8", "h-96", "h-[440px]", "h-[800px]", "h-full", "h-screen", "hidden", "hover:-translate-y-0.5", "hover:bg-cyan-500", "hover:bg-cyan-500/30", "hover:bg-cyan-600", "hover:bg-cyan-700", "hover:bg-gray-600", "hover:bg-gray-700", "hover:bg-gray-700/50", "hover:bg-red-500/10", "hover:bg-red-500/30", "hover:bg-white/30", "hover:border-blue-500/50", "hover:border-cyan-500", "hover:border-cyan-500/30", "hover:border-cyan-500/50", "hover:border-purple-500/50", "hover:from-cyan-500", "hover:from-cyan-600", "hover:scale-105", "hover:scale-110", "hover:shadow-cyan-500/10", "hover:shadow-cyan-500/25", "hover:shadow-cyan-500/40", "hover:shadow-lg", "hover:shadow-xl", "hover:text-black", "hover:text-cyan-300", "hover:text-cyan-400", "hover:text-red-400", "hover:text-white", "hover:to-blue-600", "hover:to-blue-700", "hover:underline", "inline-block", "inline-flex", "inset-0", "inset-y-0", "items-center", "items-end", "items-start", "justify-between", "justify-center", "justify-start", "leading-none", "leading-relaxed", "leading-tight", "left-0", "left-1/2", "left-1/4", "left-3", "left-4", "left-full", "lg:col-span-1", "lg:flex-row", "lg:grid-cols-3", "lg:grid-cols-4", "lg:items-center", "lg:px-8", "lg:space-y-0", "max-h-[72px]", "max-h-[90vh]", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "max-w-7xl", "max-w-[80%]", "max-w-md", "max-w-sm", "mb-1", "mb-12", "mb-16", "mb-2", "mb-20", "mb-3", "mb-4", "mb-6", "mb-8", "md:flex", "md:grid-cols-2", "md:grid-cols-3", "md:grid-cols-4", "md:hidden", "md:mt-0", "md:text-2xl", "md:text-5xl", "md:text-6xl", "md:text-8xl", "min-h-[40px]", "min-h-screen", "min-w-[40px]", "ml-2", "ml-auto", "mr-2", "mr-3", "mr-4", "mt-1", "mt-12", "mt-16", "mt-2", "mt-3", "mt-4", "mt-6", "mt-8", "mx-auto", "opacity-0", "opacity-20", "opacity-50", "opacity-60", "order-3", "overflow-auto", "overflow-hidden", "overflow-x-auto", "overflow-y-auto", "p-1", "p-2", "p-3", "p-4", "p-6", "p-8", "pb-6", "pl-10", "pl-11", "pl-12", "pl-6", "pl-8", "pr-10", "pr-3", "pr-4", "pt-3", "pt-4", "pt-6", "pt-8", "px-1", "px-2", "px-3", "px-4", "px-6", "px-8", "py-1", "py-12", "py-16", "py-2", "py-20", "py-3", "py-4", "py-8", "relative", "right-0", "right-1/4", "right-2", "right-3", "right-4", "right-8", "right-full", "rounded", "rounded-2xl", "rounded-full", "rounded-lg", "rounded-sm", "rounded-xl", "shadow-2xl", "shadow-cyan-500/25", "shadow-cyan-500/50", "shadow-lg", "shadow-md", "shadow-xl", "sm:flex-row", "sm:px-6", "space-x-1", "space-x-2", "space-x-3", "space-x-4", "space-x-8", "space-y-1", "space-y-2", "space-y-20", "space-y-3", "space-y-4", "space-y-6", "space-y-8", "sticky", "text-2xl", "text-3xl", "text-4xl", "text-5xl", "text-6xl", "text-black", "text-black/60", "text-blue-300", "text-blue-400", "text-blue-500", "text-center", "text-cyan-100", "text-cyan-300", "text-cyan-400", "text-cyan-400/80", "text-gray-300", "text-gray-400", "text-gray-500", "text-gray-600", "text-green-300", "text-green-400", "text-left", "text-lg", "text-md", "text-purple-400", "text-red-300", "text-red-400", "text-red-500", "text-right", "text-sm", "text-transparent", "text-white", "text-xl", "text-xs", "text-yellow-200", "text-yellow-300", "text-yellow-400", "top-0", "top-1/2", "top-1/3", "top-2", "top-20", "top-4", "top-full", "transform", "transition-all", "transition-colors", "transition-opacity", "transition-transform", "w-1", "w-1.5", "w-16", "w-2", "w-20", "w-3", "w-4", "w-5", "w-6", "w-64", "w-8", "w-80", "w-96", "w-[800px]", "w-full", "whitespace-nowrap", "whitespace-pre-wrap", "z-10", "z-40", "z-50"], "analysis": {"classFrequency": {}, "highFrequencyClasses": [["flex", 188], ["border", 177], ["rounded", 175], ["items-center", 112], ["text-gray-400", 90], ["text-sm", 88], ["text-white", 87], ["grid", 76], ["rounded-lg", 64], ["rounded-full", 61], ["text-cyan-400", 58], ["bg-gradient-to-r", 52], ["text-xs", 51], ["gap-2", 51], ["gap-[2px]", 51], ["absolute", 44], ["bg-gray-800", 44], ["font-semibold", 42], ["transition-colors", 39], ["mb-2", 39], ["font-bold", 38], ["justify-center", 37], ["border-gray-700", 37], ["text-center", 35], ["relative", 32], ["transition-all", 32], ["mx-auto", 32], ["mb-4", 32], ["text-lg", 31], ["p-4", 28], ["text-2xl", 27], ["font-medium", 26], ["px-4", 25], ["text-gray-300", 25], ["py-2", 24], ["inset-0", 23], ["text-black", 23], ["duration-300", 22], ["justify-between", 22], ["text-xl", 22], ["py-1", 21], ["shadow-lg", 20], ["w-full", 20], ["text-gray-500", 20], ["text-green-400", 19], ["bg-gray-700", 19], ["gap-3", 19], ["grid-cols-1", 19], ["px-6", 18], ["mb-8", 18], ["px-2", 18], ["rounded-xl", 17], ["p-6", 17], ["animate-pulse", 15], ["bg-gray-900", 15], ["transform", 15], ["mb-3", 15], ["hidden", 14], ["text-yellow-400", 14], ["gap-4", 14], ["mb-6", 14], ["py-3", 13], ["bg-cyan-500", 13], ["h-2", 13], ["duration-200", 13], ["block", 12], ["inline-flex", 12], ["hover:text-cyan-400", 12], ["text-4xl", 12], ["fixed", 11], ["z-50", 11], ["text-5xl", 11], ["text-red-400", 11], ["bg-black", 10], ["top-1/2", 10], ["bg-clip-text", 10], ["text-transparent", 10], ["md:grid-cols-2", 10], ["p-3", 10], ["animate-fade-in-up", 10], ["mt-1", 10], ["bg-red-500", 9], ["mt-2", 9], ["p-2", 9], ["flex-col", 9], ["z-10", 9], ["overflow-hidden", 9], ["gap-6", 9], ["items-start", 9], ["flex-1", 9], ["bg-gray-800/50", 9], ["hover:text-white", 9], ["space-y-2", 8], ["border-t", 8], ["max-w-7xl", 8], ["sm:px-6", 8], ["lg:px-8", 8], ["mb-16", 8], ["text-3xl", 8], ["mt-4", 8], ["hover:border-cyan-500/50", 8], ["space-y-3", 8], ["rounded-2xl", 8], ["p-8", 8], ["animate-scale-in", 7], ["gap-1", 7], ["py-20", 7], ["flex-shrink-0", 7], ["space-y-1", 7], ["right-4", 6], ["px-3", 6], ["opacity-50", 6], ["mt-6", 6], ["max-w-3xl", 6], ["h-[800px]", 6], ["border-b", 6], ["hover:scale-105", 6], ["focus:border-cyan-500", 6], ["focus:outline-none", 6], ["space-y-4", 6], ["shadow-cyan-500/25", 5], ["right-0", 5], ["hover:bg-gray-600", 5], ["h-full", 5], ["font-mono", 5], ["text-cyan-300", 5], ["bg-gradient-to-br", 5], ["hover:bg-gray-700", 5], ["border-gray-800", 5], ["font-black", 5], ["py-4", 5], ["space-y-6", 5], ["disabled:opacity-50", 5], ["disabled:cursor-not-allowed", 5], ["w-[800px]", 5]]}, "suggestions": [{"type": "component-extraction", "title": "🔄 组件提取建议", "description": "以下类名使用频率较高，建议提取为可复用组件：", "items": ["flex (使用 188 次)", "border (使用 177 次)", "rounded (使用 175 次)", "items-center (使用 112 次)", "text-gray-400 (使用 90 次)", "text-sm (使用 88 次)", "text-white (使用 87 次)", "grid (使用 76 次)", "rounded-lg (使用 64 次)", "rounded-full (使用 61 次)"]}, {"type": "class-consolidation", "title": "🎯 类名合并建议", "description": "以下相似类名可能可以合并：", "items": ["text-* 类 (37 个): text-cyan-400, text-lg, text-white, text-cyan-100, text-xs...", "bg-* 类 (54 个): bg-black, bg-cyan-900/90, bg-gradient-to-r, bg-red-500, bg-gray-800...", "top-* 类 (7 个): top-4, top-1/2, top-20, top-full, top-0...", "right-* 类 (7 个): right-4, right-8, right-full, right-0, right-3...", "px-* 类 (6 个): px-4, px-3, px-6, px-2, px-8...", "py-* 类 (8 个): py-2, py-3, py-1, py-20, py-4...", "rounded-* 类 (6 个): rounded-lg, rounded-full, rounded, rounded-sm, rounded-xl...", "shadow-* 类 (6 个): shadow-lg, shadow-cyan-500/25, shadow-md, shadow-xl, shadow-cyan-500/50...", "bottom-* 类 (5 个): bottom-8, bottom-1/4, bottom-1/3, bottom-4, bottom-0", "w-* 类 (15 个): w-16, w-5, w-64, w-full, w-80...", "h-* 类 (17 个): h-16, h-5, h-full, h-2, h-4...", "flex-* 类 (6 个): flex, flex-col, flex-1, flex-shrink-0, flex-wrap...", "hover:shadow-* 类 (5 个): hover:shadow-cyan-500/40, hover:shadow-xl, hover:shadow-cyan-500/25, hover:shadow-lg, hover:shadow-cyan-500/10", "transition-* 类 (4 个): transition-all, transition-opacity, transition-colors, transition-transform", "animate-* 类 (6 个): animate-scale-in, animate-ping, animate-pulse, animate-fade-in-up, animate-bounce...", "opacity-* 类 (4 个): opacity-20, opacity-0, opacity-60, opacity-50", "font-* 类 (5 个): font-bold, font-medium, font-semibold, font-mono, font-black", "group-* 类 (4 个): group-hover:opacity-100, group-hover:translate-x-1, group-hover:text-cyan-400, group-hover:text-cyan-300", "left-* 类 (6 个): left-full, left-1/2, left-0, left-3, left-1/4...", "border-* 类 (32 个): border-4, border-transparent, border-l-gray-800, border, border-gray-700...", "gap-* 类 (8 个): gap-2, gap-1, gap-3, gap-6, gap-4...", "mt-* 类 (8 个): mt-2, mt-3, mt-6, mt-4, mt-12...", "p-* 类 (6 个): p-4, p-2, p-3, p-6, p-8...", "mb-* 类 (9 个): mb-3, mb-8, mb-2, mb-16, mb-4...", "space-* 类 (12 个): space-y-2, space-x-4, space-x-8, space-x-3, space-x-2...", "pt-* 类 (4 个): pt-3, pt-4, pt-8, pt-6", "hover:bg-* 类 (10 个): hover:bg-cyan-500/30, hover:bg-gray-700, hover:bg-red-500/30, hover:bg-cyan-600, hover:bg-gray-600...", "max-* 类 (11 个): max-w-sm, max-w-7xl, max-w-3xl, max-w-md, max-w-4xl...", "overflow-* 类 (4 个): overflow-hidden, overflow-y-auto, overflow-auto, overflow-x-auto", "hover:text-* 类 (5 个): hover:text-cyan-300, hover:text-cyan-400, hover:text-white, hover:text-black, hover:text-red-400", "hover:border-* 类 (5 个): hover:border-cyan-500/50, hover:border-blue-500/50, hover:border-purple-500/50, hover:border-cyan-500, hover:border-cyan-500/30", "pl-* 类 (5 个): pl-10, pl-11, pl-12, pl-6, pl-8", "md:text-* 类 (4 个): md:text-8xl, md:text-2xl, md:text-6xl, md:text-5xl"]}], "optimizedConfig": {"colors": ["blue-300", "blue-400", "blue-500", "blue-700", "blue-900", "gray-300", "gray-400", "gray-500", "gray-600", "gray-700", "gray-800", "gray-900", "green-300", "green-400", "green-500", "green-700", "green-900", "purple-400", "purple-500", "red-300", "red-400", "red-500", "red-600", "red-700", "red-900", "yellow-200", "yellow-300", "yellow-400", "yellow-500", "yellow-600", "yellow-900"], "spacing": ["0", "1", "2", "3", "4", "5", "6", "7", "8", "10", "12", "16", "20", "64", "80", "96", "200", "300", "400", "500", "600", "900"], "breakpoints": ["sm", "lg", "md"]}}