// LDAP认证服务
// 通过后端API进行真实的LDAP认证
import { getLdapApiUrl } from '../config/apiConfig.js';

class LDAPService {
  constructor() {
    this.apiBaseUrl = getLdapApiUrl();
  }

  // LDAP认证主方法 - 调用后端API进行真实认证
  async authenticate(username, password, ldapEnvironment = null) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/authenticate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: username,
          password: password,
          ldapEnvironment: ldapEnvironment
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log(`[前端] 认证成功:`, result.user.name);
      
      return {
        success: true,
        user: result.user,
        ldapServer: result.ldapServer
      };
      } else {
        console.log(`[前端] 认证失败:`, result.error);
        return {
          success: false,
          error: result.error || '认证失败'
        };
      }
      
    } catch (error) {
      console.error('[前端] LDAP认证请求失败:', error);
      return {
        success: false,
        error: '网络错误，请检查LDAP认证服务是否正常运行'
      };
    }
  }



  // 获取可用的LDAP环境列表
  async getEnvironments() {
    try {
      const url = `${this.apiBaseUrl}/environments`;
      console.log('🔗 LDAP服务正在请求:', url);
      console.log('🏠 API基础URL:', this.apiBaseUrl);
      
      const response = await fetch(url);
      console.log('📡 LDAP API响应状态:', response.status, response.statusText);
      
      const result = await response.json();
      console.log('📊 LDAP API返回数据:', result);

      if (response.ok && result.success) {
        // 过滤出支持用户登录的环境（有useDirectBind标志的）
        const loginEnvironments = result.environments.filter(env => {
          // 检查环境配置中是否有useDirectBind标志，表示支持直接登录
          // 如果没有这个字段，也认为是可登录的环境
          return env.id; // 显示所有环境，让用户选择
        });

        console.log('✨ 过滤后的登录环境:', loginEnvironments);
        return {
          success: true,
          environments: loginEnvironments
        };
      } else {
        console.error('❌ [前端] 获取环境列表失败:', result.error);
        return {
          success: false,
          error: result.error || '获取环境列表失败'
        };
      }
    } catch (error) {
      console.error('💥 [前端] 获取环境列表请求失败:', error);
      return {
        success: false,
        error: '网络错误，请检查LDAP认证服务是否正常运行'
      };
    }
  }


}

// 创建单例实例
const ldapService = new LDAPService();

export default ldapService; 