// DOM错误处理工具
class DOMErrorHandler {
  constructor() {
    this.errorCount = 0;
    this.maxErrors = 10;
    this.errorLog = [];
    this.setupErrorHandlers();
  }

  setupErrorHandlers() {
    // 捕获全局DOM操作错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      
      // 检查是否是DOM相关错误
      if (this.isDOMError(message)) {
        this.handleDOMError(message);
      }
      
      // 调用原始的console.error
      originalConsoleError.apply(console, args);
    };

    // 捕获未处理的错误
    window.addEventListener('error', (event) => {
      if (this.isDOMError(event.error?.message || event.message)) {
        this.handleDOMError(event.error?.message || event.message);
        event.preventDefault(); // 阻止错误冒泡到React错误边界
      }
    });

    // 捕获Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      if (this.isDOMError(event.reason?.message || event.reason)) {
        this.handleDOMError(event.reason?.message || event.reason);
        event.preventDefault();
      }
    });
  }

  isDOMError(message) {
    if (!message || typeof message !== 'string') return false;
    
    const domErrorPatterns = [
      'removeChild',
      'appendChild',
      'replaceChild',
      'insertBefore',
      'The node to be removed is not a child',
      'Failed to execute',
      'Node',
      'DOM'
    ];
    
    return domErrorPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  handleDOMError(message) {
    this.errorCount++;
    const timestamp = new Date().toISOString();
    
    const errorInfo = {
      timestamp,
      message,
      count: this.errorCount
    };
    
    this.errorLog.push(errorInfo);
    
    // 限制错误日志大小
    if (this.errorLog.length > 50) {
      this.errorLog = this.errorLog.slice(-25);
    }
    
    console.warn(`[DOM错误处理] #${this.errorCount}: ${message}`);
    
    // 如果错误过多，可能需要禁用某些功能
    if (this.errorCount > this.maxErrors) {
      console.warn('[DOM错误处理] 检测到过多DOM错误，可能需要禁用某些功能');
      this.disableProblematicFeatures();
    }
  }

  disableProblematicFeatures() {
    // 禁用emoji替换功能
    if (window.emojiCompatibility) {
      console.warn('[DOM错误处理] 禁用emoji替换功能');
      window.emojiCompatibility.disabled = true;
      
      // 清理定时器和观察器
      if (window.emojiCompatibility.debounceTimer) {
        clearTimeout(window.emojiCompatibility.debounceTimer);
      }
      if (window.emojiCompatibility.observer) {
        window.emojiCompatibility.observer.disconnect();
      }
    }
  }

  getErrorStats() {
    return {
      totalErrors: this.errorCount,
      recentErrors: this.errorLog.slice(-10),
      isHealthy: this.errorCount < this.maxErrors
    };
  }

  reset() {
    this.errorCount = 0;
    this.errorLog = [];
    console.log('[DOM错误处理] 错误计数已重置');
  }
}

// 创建全局实例
const domErrorHandler = new DOMErrorHandler();

// 导出实例
export default domErrorHandler;

// 在开发模式下提供全局访问
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.domErrorHandler = domErrorHandler;
}
