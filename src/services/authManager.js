// 认证状态管理服务
// 包含登录超时、状态检查等功能

class AuthManager {
  constructor() {
    this.LOGIN_TIMEOUT_DAYS = 3; // 登录超时时间（天）
    this.STORAGE_KEY_USER = 'ynnxUser';
    this.STORAGE_KEY_LOGIN_TIME = 'ynnxLoginTime';
    this.STORAGE_KEY_LAST_CHECK = 'ynnxLastCheck';
    
    // 定期检查登录状态（每5分钟）
    this.CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟
    this.checkTimer = null;
    
    this.startPeriodicCheck();
  }

  /**
   * 保存用户登录状态
   * @param {Object} userData - 用户数据
   */
  saveUserLogin(userData) {
    const loginTime = new Date().getTime();
    
    // 保存用户信息和登录时间
    localStorage.setItem(this.STORAGE_KEY_USER, JSON.stringify(userData));
    localStorage.setItem(this.STORAGE_KEY_LOGIN_TIME, loginTime.toString());
    localStorage.setItem(this.STORAGE_KEY_LAST_CHECK, loginTime.toString());
    
    console.log(`[认证管理] 用户 ${userData.name} 登录成功，登录时间: ${new Date(loginTime).toLocaleString()}`);
    console.log(`[认证管理] 登录将在 ${this.LOGIN_TIMEOUT_DAYS} 天后过期`);
  }

  /**
   * 获取当前用户信息（如果未过期）
   * @returns {Object|null} 用户数据或null
   */
  getCurrentUser() {
    const userData = this.getRawUserData();
    if (!userData) {
      return null;
    }

    if (this.isLoginExpired()) {
      console.log('[认证管理] 登录已过期，自动清除用户状态');
      this.logout();
      return null;
    }

    return userData;
  }

  /**
   * 获取原始用户数据（不检查过期）
   * @returns {Object|null}
   */
  getRawUserData() {
    try {
      const userStr = localStorage.getItem(this.STORAGE_KEY_USER);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('[认证管理] 解析用户数据失败:', error);
      return null;
    }
  }

  /**
   * 检查登录是否过期
   * @returns {boolean}
   */
  isLoginExpired() {
    const loginTimeStr = localStorage.getItem(this.STORAGE_KEY_LOGIN_TIME);
    if (!loginTimeStr) {
      return true;
    }

    const loginTime = parseInt(loginTimeStr);
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - loginTime;
    const timeoutMs = this.LOGIN_TIMEOUT_DAYS * 24 * 60 * 60 * 1000;

    return timeDiff > timeoutMs;
  }

  /**
   * 获取登录剩余时间
   * @returns {Object} 包含剩余时间信息的对象
   */
  getRemainingLoginTime() {
    const loginTimeStr = localStorage.getItem(this.STORAGE_KEY_LOGIN_TIME);
    if (!loginTimeStr) {
      return { expired: true, remaining: 0 };
    }

    const loginTime = parseInt(loginTimeStr);
    const currentTime = new Date().getTime();
    const timeoutMs = this.LOGIN_TIMEOUT_DAYS * 24 * 60 * 60 * 1000;
    const expireTime = loginTime + timeoutMs;
    const remaining = expireTime - currentTime;

    if (remaining <= 0) {
      return { expired: true, remaining: 0 };
    }

    const days = Math.floor(remaining / (24 * 60 * 60 * 1000));
    const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));

    return {
      expired: false,
      remaining,
      days,
      hours,
      minutes,
      formattedTime: `${days}天${hours}小时${minutes}分钟`
    };
  }

  /**
   * 注销用户
   */
  logout() {
    const userData = this.getRawUserData();
    if (userData) {
      console.log(`[认证管理] 用户 ${userData.name} 已注销`);
    }

    localStorage.removeItem(this.STORAGE_KEY_USER);
    localStorage.removeItem(this.STORAGE_KEY_LOGIN_TIME);
    localStorage.removeItem(this.STORAGE_KEY_LAST_CHECK);
    
    // 发送注销事件
    this.dispatchAuthEvent('logout', { reason: 'manual' });
  }

  /**
   * 开始定期检查登录状态
   */
  startPeriodicCheck() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
    }

    this.checkTimer = setInterval(() => {
      this.checkLoginStatus();
    }, this.CHECK_INTERVAL);

    // 页面可见性变化时也检查
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkLoginStatus();
      }
    });
  }

  /**
   * 停止定期检查
   */
  stopPeriodicCheck() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  }

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userData = this.getRawUserData();
    if (!userData) {
      return;
    }

    const currentTime = new Date().getTime();
    
    // 更新最后检查时间
    localStorage.setItem(this.STORAGE_KEY_LAST_CHECK, currentTime.toString());

    if (this.isLoginExpired()) {
      console.log('[认证管理] 检查发现登录已过期，自动注销');
      this.logout();
      this.dispatchAuthEvent('expired', { reason: 'timeout' });
    } else {
      // 检查是否即将过期（最后1小时）
      const remaining = this.getRemainingLoginTime();
      if (!remaining.expired && remaining.remaining < 60 * 60 * 1000) { // 小于1小时
        this.dispatchAuthEvent('expiring', { remaining });
      }
    }
  }

  /**
   * 发送认证相关事件
   * @param {string} type - 事件类型
   * @param {Object} data - 事件数据
   */
  dispatchAuthEvent(type, data = {}) {
    const event = new CustomEvent('authStateChange', {
      detail: { type, ...data }
    });
    window.dispatchEvent(event);
  }

  /**
   * 延长登录时间（刷新登录状态）
   */
  refreshLoginTime() {
    const userData = this.getRawUserData();
    if (userData && !this.isLoginExpired()) {
      const newLoginTime = new Date().getTime();
      localStorage.setItem(this.STORAGE_KEY_LOGIN_TIME, newLoginTime.toString());
      localStorage.setItem(this.STORAGE_KEY_LAST_CHECK, newLoginTime.toString());
      
      console.log(`[认证管理] 登录时间已刷新，新的过期时间: ${new Date(newLoginTime + this.LOGIN_TIMEOUT_DAYS * 24 * 60 * 60 * 1000).toLocaleString()}`);
      return true;
    }
    return false;
  }

  /**
   * 获取登录状态详情
   * @returns {Object}
   */
  getLoginStatus() {
    const userData = this.getRawUserData();
    const loginTimeStr = localStorage.getItem(this.STORAGE_KEY_LOGIN_TIME);
    const remaining = this.getRemainingLoginTime();

    return {
      isLoggedIn: userData !== null && !remaining.expired,
      user: userData,
      loginTime: loginTimeStr ? new Date(parseInt(loginTimeStr)) : null,
      remaining,
      timeoutDays: this.LOGIN_TIMEOUT_DAYS
    };
  }
}

// 创建单例实例
const authManager = new AuthManager();

// 导出一个 isAuthenticated 函数，用于检查用户是否已认证
export const isAuthenticated = () => {
  return authManager.getCurrentUser() !== null;
};

export default authManager; 